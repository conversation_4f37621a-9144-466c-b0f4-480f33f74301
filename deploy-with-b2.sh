#!/bin/bash

# YendorCats Quick Deployment Script with B2 Integration
# This script sets up environment variables and deploys the application

echo "🚀 YendorCats B2 Deployment Script"
echo "======================================"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found!"
    echo "📋 Please create .env file from .env.template:"
    echo "   cp .env.template .env"
    echo "   Edit .env with your actual credentials"
    exit 1
fi

# Load environment variables
echo "📦 Loading environment variables..."
set -a  # automatically export all variables
source .env
set +a

# Validate required environment variables
echo "✅ Validating required environment variables..."
required_vars=(
    "MYSQL_USER"
    "MYSQL_PASSWORD"
    "YENDOR_JWT_SECRET"
    "B2_APPLICATION_KEY_ID"
    "B2_APPLICATION_KEY"
    "B2_BUCKET_ID"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo "📝 Please update your .env file with all required values"
    exit 1
fi

# Set AWS credentials for S3-compatible API
export AWS_ACCESS_KEY_ID="$B2_APPLICATION_KEY_ID"
export AWS_SECRET_ACCESS_KEY="$B2_APPLICATION_KEY"
export AWS_REGION="us-west-004"

# Set default values for Docker Compose
export ASPNETCORE_ENVIRONMENT="${ASPNETCORE_ENVIRONMENT:-Production}"
export MYSQL_ROOT_PASSWORD="${MYSQL_ROOT_PASSWORD:-${MYSQL_PASSWORD}}"
export AWS_S3_BUCKET_NAME="${AWS_S3_BUCKET_NAME:-yendor}"
export AWS_S3_ACCESS_KEY="$B2_APPLICATION_KEY_ID"
export AWS_S3_SECRET_KEY="$B2_APPLICATION_KEY"

echo "🔧 Configuration Summary:"
echo "   - Environment: $ASPNETCORE_ENVIRONMENT"
echo "   - Database User: $MYSQL_USER"
echo "   - S3 Bucket: $AWS_S3_BUCKET_NAME"
echo "   - B2 Key ID: ${B2_APPLICATION_KEY_ID:0:10}..."
echo "   - B2 Bucket ID: ${B2_BUCKET_ID:0:10}..."

# Build and start containers
echo "🏗️  Building and starting containers..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service status
echo "📊 Checking service status..."
docker-compose ps

# Test database connection
echo "🗄️  Testing database connection..."
docker-compose exec -T db mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
fi

# Test API endpoint
echo "🌐 Testing API endpoint..."
sleep 10
if curl -s -f http://localhost:5003/health > /dev/null 2>&1; then
    echo "✅ API is responding"
else
    echo "⚠️  API not responding yet (this is normal on first startup)"
fi

echo ""
echo "🎉 Deployment Complete!"
echo "================================"
echo "🌐 Application URLs:"
echo "   - API: http://localhost:5003"
echo "   - Admin Panel: http://localhost:5003/admin.html"
echo "   - File Uploader: http://localhost:5002"
echo "   - Database: localhost:3306"
echo ""
echo "📋 Next Steps:"
echo "1. Check logs: docker-compose logs -f api"
echo "2. Access admin panel to upload cat images"
echo "3. Monitor B2 synchronization in logs"
echo ""
echo "🔧 Troubleshooting:"
echo "   - View logs: docker-compose logs -f"
echo "   - Restart services: docker-compose restart"
echo "   - Check B2 credentials in .env file"

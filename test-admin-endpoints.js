/**
 * Simple test script to validate admin endpoints
 * Run with: node test-admin-endpoints.js
 */

const https = require('https');
const http = require('http');

// Configuration
const baseUrl = 'http://0.0.0.0:5002';
const endpoints = {
    login: '/api/AdminAuth/login',
    clients: '/api/AdminClient',
    appointments: '/api/AdminAppointment',
    userManagement: '/api/AdminUserManagement'
};

// Test data
const adminCredentials = {
    username: 'admin',
    password: 'admin123'
};

const testClient = {
    firstName: 'Test',
    lastName: 'Client',
    email: '<EMAIL>',
    phone: '+1234567890',
    address: '123 Test Street',
    city: 'Test City',
    state: 'Test State',
    postalCode: '12345',
    country: 'Test Country',
    notes: 'This is a test client',
    isActive: true
};

let authToken = null;
let createdClientId = null;

// Helper function to make HTTP requests
function makeRequest(method, path, data = null, token = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, baseUrl);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Admin-Endpoint-Tester/1.0'
            }
        };

        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }

        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = responseData ? JSON.parse(responseData) : {};
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: parsedData
                    });
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: responseData
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// Test functions
async function testLogin() {
    console.log('\n=== Testing Admin Login ===');
    
    try {
        const response = await makeRequest('POST', endpoints.login, adminCredentials);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200 && response.data.success) {
            authToken = response.data.token;
            console.log('✅ Login successful - Token received');
            return true;
        } else {
            console.log('❌ Login failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Login error:', error.message);
        return false;
    }
}

async function testGetClients() {
    console.log('\n=== Testing Get All Clients ===');
    
    try {
        const response = await makeRequest('GET', endpoints.clients, null, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200) {
            console.log('✅ Get clients successful');
            return true;
        } else {
            console.log('❌ Get clients failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Get clients error:', error.message);
        return false;
    }
}

async function testCreateClient() {
    console.log('\n=== Testing Create Client ===');
    
    try {
        const response = await makeRequest('POST', endpoints.clients, testClient, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 201 && response.data.success) {
            createdClientId = response.data.client.id;
            console.log(`✅ Create client successful - ID: ${createdClientId}`);
            return true;
        } else {
            console.log('❌ Create client failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Create client error:', error.message);
        return false;
    }
}

async function testGetClientById() {
    if (!createdClientId) {
        console.log('\n=== Skipping Get Client By ID (no client created) ===');
        return false;
    }
    
    console.log('\n=== Testing Get Client By ID ===');
    
    try {
        const response = await makeRequest('GET', `${endpoints.clients}/${createdClientId}`, null, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200 && response.data.success) {
            console.log('✅ Get client by ID successful');
            return true;
        } else {
            console.log('❌ Get client by ID failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Get client by ID error:', error.message);
        return false;
    }
}

async function testCreateAppointment() {
    if (!createdClientId) {
        console.log('\n=== Skipping Create Appointment (no client created) ===');
        return false;
    }
    
    console.log('\n=== Testing Create Appointment ===');
    
    const testAppointment = {
        title: 'Test Appointment',
        clientId: createdClientId,
        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        endTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(), // Tomorrow + 1 hour
        location: 'Test Location',
        status: 'Scheduled',
        notes: 'This is a test appointment'
    };
    
    try {
        const response = await makeRequest('POST', endpoints.appointments, testAppointment, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 201 && response.data.success) {
            console.log('✅ Create appointment successful');
            return true;
        } else {
            console.log('❌ Create appointment failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Create appointment error:', error.message);
        return false;
    }
}

async function testGetAppointments() {
    console.log('\n=== Testing Get All Appointments ===');
    
    try {
        const response = await makeRequest('GET', endpoints.appointments, null, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200) {
            console.log('✅ Get appointments successful');
            return true;
        } else {
            console.log('❌ Get appointments failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Get appointments error:', error.message);
        return false;
    }
}

async function testUserManagement() {
    console.log('\n=== Testing User Management - Get All Users ===');
    
    try {
        const response = await makeRequest('GET', endpoints.userManagement, null, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200) {
            console.log('✅ Get users successful');
            return true;
        } else {
            console.log('❌ Get users failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Get users error:', error.message);
        return false;
    }
}

async function testPasswordChange() {
    console.log('\n=== Testing Password Change ===');
    
    const passwordChangeData = {
        currentPassword: 'admin123',
        newPassword: 'newPassword123',
        confirmPassword: 'newPassword123'
    };
    
    try {
        const response = await makeRequest('POST', '/api/AdminUserManagement/change-password', passwordChangeData, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200 && response.data.success) {
            console.log('✅ Password change successful');
            // Update credentials for subsequent tests
            adminCredentials.password = 'newPassword123';
            return true;
        } else {
            console.log('❌ Password change failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Password change error:', error.message);
        return false;
    }
}

async function testMetadataAccess() {
    console.log('\n=== Testing Metadata Access (Admin Protected) ===');
    
    try {
        const response = await makeRequest('GET', '/api/S3Metadata', null, authToken);
        
        console.log(`Status: ${response.statusCode}`);
        console.log(`Response:`, JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200) {
            console.log('✅ Metadata access successful (admin protected)');
            return true;
        } else {
            console.log('❌ Metadata access failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Metadata access error:', error.message);
        return false;
    }
}

// Main test runner
async function runTests() {
    console.log('🚀 Starting Admin Endpoint Tests');
    console.log(`Testing against: ${baseUrl}`);
    
    const results = [];
    
    // Test login first
    const loginSuccess = await testLogin();
    results.push({ test: 'Login', success: loginSuccess });
    
    if (!loginSuccess) {
        console.log('\n❌ Cannot proceed without successful login');
        return;
    }
    
    // Change password first (required for default admin account)
    const passwordChangeSuccess = await testPasswordChange();
    results.push({ test: 'Password Change', success: passwordChangeSuccess });
    
    if (!passwordChangeSuccess) {
        console.log('\n❌ Cannot proceed without password change');
        return;
    }
    
    // Re-login with new password
    console.log('\n=== Re-authenticating with new password ===');
    const reLoginSuccess = await testLogin();
    results.push({ test: 'Re-login after password change', success: reLoginSuccess });
    
    if (!reLoginSuccess) {
        console.log('\n❌ Re-authentication failed');
        return;
    }
    
    // Test all other endpoints
    const tests = [
        { name: 'Get Clients', fn: testGetClients },
        { name: 'Create Client', fn: testCreateClient },
        { name: 'Get Client By ID', fn: testGetClientById },
        { name: 'Get Appointments', fn: testGetAppointments },
        { name: 'Create Appointment', fn: testCreateAppointment },
        { name: 'User Management', fn: testUserManagement },
        { name: 'Metadata Access', fn: testMetadataAccess }
    ];
    
    for (const test of tests) {
        const success = await test.fn();
        results.push({ test: test.name, success });
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(50));
    
    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${result.test}`);
    });
    
    const passed = results.filter(r => r.success).length;
    const total = results.length;
    
    console.log(`\n📈 Results: ${passed}/${total} tests passed`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Admin system is working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Please check the API implementation.');
    }
}

// Run the tests
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { runTests };

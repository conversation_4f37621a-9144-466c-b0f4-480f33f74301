---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: project
status: <% await tp.system.suggester(["active", "planning", "on-hold", "completed", "cancelled"], ["active", "planning", "on-hold", "completed", "cancelled"], false, "Project Status") %>
priority: <% await tp.system.suggester(["high", "medium", "low"], ["high", "medium", "low"], false, "Priority Level") %>
deadline: <% await tp.system.prompt("Deadline (YYYY-MM-DD)", tp.date.now("YYYY-MM-DD", 30)) %>
project_owner: <% await tp.system.suggester(["Jordan", "Other"], ["Jordan", await tp.system.prompt("Project Owner")], false, "Project Owner") %>
project_client: <% await tp.system.suggester(["Personal", "Church", "University", "Client"], ["Personal", "Church", "University", await tp.system.prompt("Client Name")], false, "Project Client") %>
completion_percentage: <% await tp.system.prompt("Completion Percentage (0-100)", "0") %>
estimated_hours: <% await tp.system.prompt("Estimated Hours", "20") %>
area: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "University", "Other"], ["Software-Development", "Administration", "Personal", "Church", "University", await tp.system.prompt("Area Name")], false, "Related Area") %>
start_date: <% tp.date.now("YYYY-MM-DD") %>
tags: [para/projects, <% await tp.system.suggester(["software-dev", "church", "personal", "university", "admin", "finance", "compliance", "critical", "urgent", "important"], ["software-dev", "church", "personal", "university", "admin", "finance", "compliance", "critical", "urgent", "important"], false, "Primary Tag") %>]
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
priority_score: 0
---

# <% tp.file.title %>

<%* 
// Auto-calculate priority score
const deadline = tp.frontmatter.deadline;
const priority = tp.frontmatter.priority;
const tags = tp.frontmatter.tags || [];

let score = 0;

// Due date weight
if (deadline) {
  const deadlineDate = new Date(deadline);
  const today = new Date();
  const diffTime = deadlineDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) score += 100; // Overdue
  else if (diffDays === 0) score += 80; // Today
  else if (diffDays <= 7) score += 60; // This week
  else if (diffDays <= 30) score += 40; // This month
  else score += 20; // Later
}

// Priority level weight
if (priority === "high") score += 30;
else if (priority === "medium") score += 20;
else score += 10;

// Tag importance weights
const tagWeights = {
  "critical": 25,
  "urgent": 20,
  "important": 15,
  "church": 15,
  "admin": 10,
  "personal": 5,
  "finance": 20,
  "compliance": 25
};

tags.forEach(tag => {
  if (tagWeights[tag]) {
    score += tagWeights[tag];
  }
});

// Update frontmatter with calculated score
const currentFile = tp.file.find_tfile(tp.file.path(true));
if (currentFile) {
  const content = await app.vault.read(currentFile);
  const updatedContent = content.replace(/priority_score: \d+/, `priority_score: ${score}`);
  await app.vault.modify(currentFile, updatedContent);
}
%>

## Overview
<!-- Brief description of the project -->

## Objectives
<!-- What are you trying to achieve? -->
- 

## Success Criteria
<!-- How will you know when the project is successful? -->
- 

## Tasks
<!-- List of tasks to complete -->
- [ ] 

## Timeline
- **Start Date**: <% tp.date.now("YYYY-MM-DD") %>
- **Deadline**: <% await tp.system.prompt("Deadline (YYYY-MM-DD)", tp.date.now("YYYY-MM-DD", 30)) %>
- **Milestones**:
  - [ ] Initial Planning - <% tp.date.now("YYYY-MM-DD", 7) %>
  - [ ] Development - <% tp.date.now("YYYY-MM-DD", 14) %>
  - [ ] Testing - <% tp.date.now("YYYY-MM-DD", 21) %>
  - [ ] Completion - <% tp.date.now("YYYY-MM-DD", 30) %>

## Resources
<!-- Links to relevant resources -->
- 

<%* 
// Smart relationship queries - only show if relationships exist
const related = tp.frontmatter.related || {};
let hasRelated = false;

// Check if any relationship arrays have content
for (const key in related) {
  if (related[key] && related[key].length > 0) {
    hasRelated = true;
    break;
  }
}

if (hasRelated) {
  tR += "\n## 🔗 Relationships\n";
  
  // Project Dependencies (highest priority)
  if (related["depends-on"] && related["depends-on"].length > 0) {
    tR += "\n<details><summary>🚨 Project Dependencies (" + related["depends-on"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Project\",\n";
    tR += "  status as \"Status\",\n";
    tR += "  completion_percentage + \"%\" as \"Progress\",\n";
    tR += "  deadline as \"Deadline\"\n";
    tR += "FROM \"1-Projects\"\n";
    tR += "WHERE file.name IN [";
    related["depends-on"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["depends-on"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT deadline ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // What this project blocks
  if (related["blocks"] && related["blocks"].length > 0) {
    tR += "\n<details><summary>⚡ Blocks These Projects (" + related["blocks"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Project\",\n";
    tR += "  status as \"Status\",\n";
    tR += "  priority as \"Priority\"\n";
    tR += "FROM \"1-Projects\"\n";
    tR += "WHERE file.name IN [";
    related["blocks"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["blocks"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT priority ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // Area overlaps
  if (related["area-overlap"] && related["area-overlap"].length > 0) {
    tR += "\n<details><summary>📊 Area Overlaps (" + related["area-overlap"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Area\",\n";
    tR += "  responsibility_level as \"Responsibility\",\n";
    tR += "  last_review_date as \"Last Review\"\n";
    tR += "FROM \"2-Areas\"\n";
    tR += "WHERE file.name IN [";
    related["area-overlap"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["area-overlap"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT responsibility_level ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // References
  if (related["references"] && related["references"].length > 0) {
    tR += "\n<details><summary>📚 References (" + related["references"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  source as \"Source\",\n";
    tR += "  difficulty as \"Difficulty\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE file.name IN [";
    related["references"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["references"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT difficulty ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
}
%>

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related.relates-to, "<% tp.file.title %>") OR contains(file.name, "<% tp.file.title %>")
SORT date DESC
```

## Progress Updates
<!-- Regular updates on project progress -->
### <% tp.date.now("YYYY-MM-DD") %> - Initial Setup
- Project created
- Initial planning started

<%* 
// Auto-generated "See Also" based on shared tags and area
const currentArea = tp.frontmatter.area;
const currentTags = tp.frontmatter.tags || [];

if (currentArea || currentTags.length > 0) {
  tR += "\n## 🔍 See Also\n";
  
  // Related projects in same area
  if (currentArea) {
    tR += "\n### Related Projects\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Project\",\n";
    tR += "  status as \"Status\",\n";
    tR += "  completion_percentage + \"%\" as \"Progress\"\n";
    tR += "FROM \"1-Projects\"\n";
    tR += "WHERE area = \"" + currentArea + "\" AND file.name != \"" + tp.file.title + "\"\n";
    tR += "SORT completion_percentage ASC\n";
    tR += "LIMIT 5\n";
    tR += "```\n";
  }
  
  // Related areas
  if (currentArea) {
    tR += "\n### Related Areas\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Area\",\n";
    tR += "  responsibility_level as \"Responsibility\"\n";
    tR += "FROM \"2-Areas\"\n";
    tR += "WHERE area = \"" + currentArea + "\" OR file.name = \"" + currentArea + "\"\n";
    tR += "```\n";
  }
  
  // Related resources by tags
  if (currentTags.length > 0) {
    tR += "\n### Related Resources\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  source as \"Source\",\n";
    tR += "  difficulty as \"Difficulty\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE ";
    currentTags.forEach((tag, index) => {
      tR += "contains(tags, \"" + tag + "\")";
      if (index < currentTags.length - 1) tR += " OR ";
    });
    tR += "\nSORT difficulty ASC\n";
    tR += "LIMIT 5\n";
    tR += "```\n";
  }
}
%>

## Quick Links
- [[<% tp.file.title %> Meeting|New Meeting]]
- [[<% tp.file.title %> Resource|New Resource]]
- [[1-Projects|All Projects]]

---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: area
status: <% await tp.system.suggester(["active", "maintenance", "reviewing", "inactive"], ["active", "maintenance", "reviewing", "inactive"], false, "Area Status") %>
area: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "University", "Other"], ["Software-Development", "Administration", "Personal", "Church", "University", await tp.system.prompt("Area Category")], false, "Area Category") %>
area_owner: <% await tp.system.suggester(["Jordan", "Other"], ["Jordan", await tp.system.prompt("Area Owner")], false, "Area Owner") %>
responsibility_level: <% await tp.system.suggester(["critical", "high", "medium", "low"], ["critical", "high", "medium", "low"], false, "Responsibility Level") %>
review_frequency: <% await tp.system.suggester(["daily", "weekly", "monthly", "quarterly"], ["daily", "weekly", "monthly", "quarterly"], false, "Review Frequency") %>
organization: <% await tp.system.prompt("Organization (if applicable)", "") %>
tags: [para/areas, <% await tp.system.suggester(["software-dev", "administration", "church", "personal", "university", "finance", "compliance", "critical", "urgent", "important"], ["software-dev", "administration", "church", "personal", "university", "finance", "compliance", "critical", "urgent", "important"], false, "Primary Tag") %>]
last_review_date: <% tp.date.now("YYYY-MM-DD") %>
next_review_date: <% tp.date.now("YYYY-MM-DD", 30) %>
key_contacts: []
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
priority_score: 0
---

# <% tp.file.title %>

<%* 
// Auto-calculate area priority score
const responsibility = tp.frontmatter.responsibility_level;
const reviewFreq = tp.frontmatter.review_frequency;
const tags = tp.frontmatter.tags || [];
const nextReview = tp.frontmatter.next_review_date;

let score = 0;

// Responsibility level weight
if (responsibility === "critical") score += 40;
else if (responsibility === "high") score += 30;
else if (responsibility === "medium") score += 20;
else score += 10;

// Review frequency weight (more frequent = higher priority)
if (reviewFreq === "daily") score += 30;
else if (reviewFreq === "weekly") score += 20;
else if (reviewFreq === "monthly") score += 15;
else score += 10;

// Overdue review penalty
if (nextReview) {
  const reviewDate = new Date(nextReview);
  const today = new Date();
  const diffTime = reviewDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) score += 25; // Overdue review
  else if (diffDays <= 7) score += 15; // Due soon
}

// Tag importance weights
const tagWeights = {
  "critical": 25,
  "urgent": 20,
  "important": 15,
  "church": 15,
  "admin": 10,
  "personal": 5,
  "finance": 20,
  "compliance": 25
};

tags.forEach(tag => {
  if (tagWeights[tag]) {
    score += tagWeights[tag];
  }
});

// Update frontmatter with calculated score
const currentFile = tp.file.find_tfile(tp.file.path(true));
if (currentFile) {
  const content = await app.vault.read(currentFile);
  const updatedContent = content.replace(/priority_score: \d+/, `priority_score: ${score}`);
  await app.vault.modify(currentFile, updatedContent);
}
%>

## Overview
<!-- Brief description of this area of responsibility -->

## Current Focus
<!-- What's the current focus in this area? -->
- 

## Key Responsibilities
<!-- List the key responsibilities in this area -->
- 

## Regular Tasks
<!-- Recurring tasks in this area -->
- [ ] Daily: 
- [ ] Weekly: 
- [ ] Monthly: 

<%* 
// Smart relationship queries - only show if relationships exist
const related = tp.frontmatter.related || {};
let hasRelated = false;

// Check if any relationship arrays have content
for (const key in related) {
  if (related[key] && related[key].length > 0) {
    hasRelated = true;
    break;
  }
}

if (hasRelated) {
  tR += "\n## 🔗 Relationships\n";
  
  // Area Dependencies (highest priority)
  if (related["depends-on"] && related["depends-on"].length > 0) {
    tR += "\n<details><summary>🚨 Area Dependencies (" + related["depends-on"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Area\",\n";
    tR += "  responsibility_level as \"Responsibility\",\n";
    tR += "  status as \"Status\",\n";
    tR += "  next_review_date as \"Next Review\"\n";
    tR += "FROM \"2-Areas\"\n";
    tR += "WHERE file.name IN [";
    related["depends-on"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["depends-on"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT responsibility_level ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // What this area blocks
  if (related["blocks"] && related["blocks"].length > 0) {
    tR += "\n<details><summary>⚡ Blocks These Areas (" + related["blocks"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Area\",\n";
    tR += "  responsibility_level as \"Responsibility\",\n";
    tR += "  status as \"Status\"\n";
    tR += "FROM \"2-Areas\"\n";
    tR += "WHERE file.name IN [";
    related["blocks"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["blocks"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT responsibility_level ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // Area overlaps
  if (related["area-overlap"] && related["area-overlap"].length > 0) {
    tR += "\n<details><summary>📊 Area Overlaps (" + related["area-overlap"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Area\",\n";
    tR += "  responsibility_level as \"Responsibility\",\n";
    tR += "  area_owner as \"Owner\",\n";
    tR += "  last_review_date as \"Last Review\"\n";
    tR += "FROM \"2-Areas\"\n";
    tR += "WHERE file.name IN [";
    related["area-overlap"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["area-overlap"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT responsibility_level ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // References
  if (related["references"] && related["references"].length > 0) {
    tR += "\n<details><summary>📚 References (" + related["references"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  source as \"Source\",\n";
    tR += "  difficulty as \"Difficulty\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE file.name IN [";
    related["references"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["references"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT difficulty ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
}
%>

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "<% tp.file.title %>" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[<% tp.file.title %>]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
- 

<%* 
// Auto-generated "See Also" based on shared tags and area
const currentArea = tp.frontmatter.area;
const currentTags = tp.frontmatter.tags || [];

if (currentArea || currentTags.length > 0) {
  tR += "\n## 🔍 See Also\n";
  
  // Related areas in same category
  if (currentArea) {
    tR += "\n### Related Areas\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Area\",\n";
    tR += "  responsibility_level as \"Responsibility\",\n";
    tR += "  status as \"Status\"\n";
    tR += "FROM \"2-Areas\"\n";
    tR += "WHERE area = \"" + currentArea + "\" AND file.name != \"" + tp.file.title + "\"\n";
    tR += "SORT responsibility_level ASC\n";
    tR += "```\n";
  }
  
  // Related projects
  if (currentArea) {
    tR += "\n### Related Projects\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Project\",\n";
    tR += "  status as \"Status\",\n";
    tR += "  completion_percentage + \"%\" as \"Progress\"\n";
    tR += "FROM \"1-Projects\"\n";
    tR += "WHERE area = \"" + currentArea + "\"\n";
    tR += "SORT completion_percentage ASC\n";
    tR += "LIMIT 5\n";
    tR += "```\n";
  }
  
  // Related resources by tags
  if (currentTags.length > 0) {
    tR += "\n### Related Resources\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  source as \"Source\",\n";
    tR += "  difficulty as \"Difficulty\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE ";
    currentTags.forEach((tag, index) => {
      tR += "contains(tags, \"" + tag + "\")";
      if (index < currentTags.length - 1) tR += " OR ";
    });
    tR += "\nSORT difficulty ASC\n";
    tR += "LIMIT 5\n";
    tR += "```\n";
  }
}
%>

## Quick Links
- [[<% tp.file.title %> Project|New Project]]
- [[<% tp.file.title %> Resource|New Resource]]
- [[<% tp.file.title %> Meeting|New Meeting]]
- [[2-Areas|All Areas]]

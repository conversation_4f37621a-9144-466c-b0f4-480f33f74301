using System;
using System.Collections.Generic;
using System.Linq;

namespace YendorCats.API.Models.DTOs
{
    /// <summary>
    /// Result of a migration operation with comprehensive tracking
    /// </summary>
    public class MigrationResult
    {
        /// <summary>
        /// Unique identifier for this migration operation
        /// </summary>
        public string MigrationId { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// Overall success status of the migration
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// Current status of the migration
        /// </summary>
        public string Status { get; set; } = "PENDING"; // PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
        
        /// <summary>
        /// Total number of items to be processed
        /// </summary>
        public int TotalItems { get; set; }
        
        /// <summary>
        /// Number of items processed (both success and failure)
        /// </summary>
        public int ProcessedItems { get; set; }
        
        /// <summary>
        /// Number of successful migrations
        /// </summary>
        public int SuccessfulMigrations { get; set; }
        
        /// <summary>
        /// Number of failed migrations
        /// </summary>
        public int FailedMigrations { get; set; }
        
        /// <summary>
        /// Number of items skipped (duplicates, invalid, etc.)
        /// </summary>
        public int SkippedItems { get; set; }
        
        /// <summary>
        /// List of errors encountered during migration
        /// </summary>
        public List<string> Errors { get; set; } = new();
        
        /// <summary>
        /// List of warnings encountered during migration
        /// </summary>
        public List<string> Warnings { get; set; } = new();
        
        /// <summary>
        /// Total duration of the migration
        /// </summary>
        public TimeSpan Duration { get; set; }
        
        /// <summary>
        /// When the migration started
        /// </summary>
        public DateTime StartedAt { get; set; }
        
        /// <summary>
        /// When the migration completed (null if still running)
        /// </summary>
        public DateTime? CompletedAt { get; set; }
        
        /// <summary>
        /// Source storage provider
        /// </summary>
        public string SourceProvider { get; set; } = string.Empty;
        
        /// <summary>
        /// Destination storage provider
        /// </summary>
        public string DestinationProvider { get; set; } = string.Empty;
        
        /// <summary>
        /// Migration type (FULL, INCREMENTAL, CATEGORY, etc.)
        /// </summary>
        public string MigrationType { get; set; } = "FULL";
        
        /// <summary>
        /// Category filter if applicable
        /// </summary>
        public string? CategoryFilter { get; set; }
        
        /// <summary>
        /// Batch size used for processing
        /// </summary>
        public int BatchSize { get; set; } = 10;
        
        /// <summary>
        /// User who initiated the migration
        /// </summary>
        public string? InitiatedBy { get; set; }
        
        /// <summary>
        /// Detailed migration statistics
        /// </summary>
        public MigrationStatistics Statistics { get; set; } = new();
        
        /// <summary>
        /// Performance metrics
        /// </summary>
        public MigrationPerformance Performance { get; set; } = new();
        
        /// <summary>
        /// List of individual item results
        /// </summary>
        public List<MigrationItemResult> ItemResults { get; set; } = new();
        
        /// <summary>
        /// Additional context or notes
        /// </summary>
        public string? Notes { get; set; }
        
        // Computed properties
        public int RemainingItems => TotalItems - ProcessedItems;
        public double ProgressPercentage => TotalItems > 0 ? (double)ProcessedItems / TotalItems * 100 : 0;
        public double SuccessRate => ProcessedItems > 0 ? (double)SuccessfulMigrations / ProcessedItems * 100 : 0;
        public bool IsCompleted => Status == "COMPLETED" || Status == "FAILED" || Status == "CANCELLED";
        public bool IsRunning => Status == "RUNNING";
        public bool IsPending => Status == "PENDING";
        public TimeSpan EstimatedTimeRemaining => CalculateEstimatedTimeRemaining();
        public double ItemsPerSecond => Duration.TotalSeconds > 0 ? ProcessedItems / Duration.TotalSeconds : 0;
        
        /// <summary>
        /// Start the migration
        /// </summary>
        public void Start()
        {
            StartedAt = DateTime.UtcNow;
            Status = "RUNNING";
        }
        
        /// <summary>
        /// Complete the migration successfully
        /// </summary>
        public void Complete()
        {
            CompletedAt = DateTime.UtcNow;
            Duration = CompletedAt.Value - StartedAt;
            Status = "COMPLETED";
            Success = FailedMigrations == 0;
        }
        
        /// <summary>
        /// Mark migration as failed
        /// </summary>
        public void Fail(string errorMessage)
        {
            CompletedAt = DateTime.UtcNow;
            Duration = CompletedAt.Value - StartedAt;
            Status = "FAILED";
            Success = false;
            Errors.Add(errorMessage);
        }
        
        /// <summary>
        /// Cancel the migration
        /// </summary>
        public void Cancel()
        {
            CompletedAt = DateTime.UtcNow;
            Duration = CompletedAt.Value - StartedAt;
            Status = "CANCELLED";
            Success = false;
            Warnings.Add("Migration was cancelled");
        }
        
        /// <summary>
        /// Add a successful item result
        /// </summary>
        public void AddSuccess(string itemKey, string? message = null)
        {
            ProcessedItems++;
            SuccessfulMigrations++;
            ItemResults.Add(new MigrationItemResult
            {
                ItemKey = itemKey,
                Success = true,
                Message = message,
                ProcessedAt = DateTime.UtcNow
            });
        }
        
        /// <summary>
        /// Add a failed item result
        /// </summary>
        public void AddFailure(string itemKey, string errorMessage)
        {
            ProcessedItems++;
            FailedMigrations++;
            Errors.Add($"{itemKey}: {errorMessage}");
            ItemResults.Add(new MigrationItemResult
            {
                ItemKey = itemKey,
                Success = false,
                ErrorMessage = errorMessage,
                ProcessedAt = DateTime.UtcNow
            });
        }
        
        /// <summary>
        /// Add a skipped item result
        /// </summary>
        public void AddSkipped(string itemKey, string reason)
        {
            ProcessedItems++;
            SkippedItems++;
            Warnings.Add($"{itemKey}: {reason}");
            ItemResults.Add(new MigrationItemResult
            {
                ItemKey = itemKey,
                Success = true,
                Skipped = true,
                Message = reason,
                ProcessedAt = DateTime.UtcNow
            });
        }
        
        /// <summary>
        /// Add a warning
        /// </summary>
        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
        
        /// <summary>
        /// Get migration summary
        /// </summary>
        public string GetSummary()
        {
            if (IsCompleted)
            {
                return $"Migration {MigrationId} completed in {Duration:hh\\:mm\\:ss}. " +
                       $"Processed {ProcessedItems}/{TotalItems} items. " +
                       $"Success: {SuccessfulMigrations}, Failed: {FailedMigrations}, Skipped: {SkippedItems}";
            }
            else if (IsRunning)
            {
                return $"Migration {MigrationId} in progress. " +
                       $"Processed {ProcessedItems}/{TotalItems} items ({ProgressPercentage:F1}%). " +
                       $"ETA: {EstimatedTimeRemaining:hh\\:mm\\:ss}";
            }
            else
            {
                return $"Migration {MigrationId} is {Status.ToLower()}. {TotalItems} items queued.";
            }
        }
        
        /// <summary>
        /// Calculate estimated time remaining
        /// </summary>
        private TimeSpan CalculateEstimatedTimeRemaining()
        {
            if (ProcessedItems == 0 || IsCompleted)
                return TimeSpan.Zero;
            
            var currentDuration = DateTime.UtcNow - StartedAt;
            var averageTimePerItem = currentDuration.TotalSeconds / ProcessedItems;
            var remainingSeconds = RemainingItems * averageTimePerItem;
            
            return TimeSpan.FromSeconds(remainingSeconds);
        }
        
        /// <summary>
        /// Get failed items
        /// </summary>
        public List<MigrationItemResult> GetFailedItems()
        {
            return ItemResults.Where(r => !r.Success && !r.Skipped).ToList();
        }
        
        /// <summary>
        /// Get skipped items
        /// </summary>
        public List<MigrationItemResult> GetSkippedItems()
        {
            return ItemResults.Where(r => r.Skipped).ToList();
        }
        
        /// <summary>
        /// Get successful items
        /// </summary>
        public List<MigrationItemResult> GetSuccessfulItems()
        {
            return ItemResults.Where(r => r.Success && !r.Skipped).ToList();
        }
    }
    
    /// <summary>
    /// Result of migrating a single item
    /// </summary>
    public class MigrationItemResult
    {
        public string ItemKey { get; set; } = string.Empty;
        public bool Success { get; set; }
        public bool Skipped { get; set; }
        public string? Message { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime ProcessedAt { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public long? FileSize { get; set; }
        public string? SourceProvider { get; set; }
        public string? DestinationProvider { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
    }
    
    /// <summary>
    /// Migration statistics
    /// </summary>
    public class MigrationStatistics
    {
        public int TotalCategories { get; set; }
        public Dictionary<string, int> CategoryCounts { get; set; } = new();
        public Dictionary<string, int> FileTypeCounts { get; set; } = new();
        public Dictionary<string, int> StatusCounts { get; set; } = new();
        public long TotalFileSize { get; set; }
        public long AverageFileSize { get; set; }
        public long LargestFileSize { get; set; }
        public long SmallestFileSize { get; set; }
        public string? LargestFileName { get; set; }
        public string? SmallestFileName { get; set; }
        public int UniqueMetadataFields { get; set; }
        public Dictionary<string, int> MetadataFieldCounts { get; set; } = new();
        
        /// <summary>
        /// Update statistics with a new item
        /// </summary>
        public void UpdateWithItem(string category, string fileType, long fileSize, 
            string fileName, Dictionary<string, string>? metadata = null)
        {
            // Update category counts
            if (!string.IsNullOrEmpty(category))
            {
                CategoryCounts[category] = CategoryCounts.GetValueOrDefault(category, 0) + 1;
            }
            
            // Update file type counts
            if (!string.IsNullOrEmpty(fileType))
            {
                FileTypeCounts[fileType] = FileTypeCounts.GetValueOrDefault(fileType, 0) + 1;
            }
            
            // Update file size statistics
            TotalFileSize += fileSize;
            if (LargestFileSize == 0 || fileSize > LargestFileSize)
            {
                LargestFileSize = fileSize;
                LargestFileName = fileName;
            }
            if (SmallestFileSize == 0 || fileSize < SmallestFileSize)
            {
                SmallestFileSize = fileSize;
                SmallestFileName = fileName;
            }
            
            // Update metadata statistics
            if (metadata != null)
            {
                foreach (var key in metadata.Keys)
                {
                    MetadataFieldCounts[key] = MetadataFieldCounts.GetValueOrDefault(key, 0) + 1;
                }
            }
        }
        
        /// <summary>
        /// Finalize statistics calculations
        /// </summary>
        public void Finalize(int totalItems)
        {
            TotalCategories = CategoryCounts.Count;
            AverageFileSize = totalItems > 0 ? TotalFileSize / totalItems : 0;
            UniqueMetadataFields = MetadataFieldCounts.Count;
        }
    }
    
    /// <summary>
    /// Migration performance metrics
    /// </summary>
    public class MigrationPerformance
    {
        public double ItemsPerSecond { get; set; }
        public double MegabytesPerSecond { get; set; }
        public TimeSpan AverageItemProcessingTime { get; set; }
        public TimeSpan FastestItemProcessingTime { get; set; }
        public TimeSpan SlowestItemProcessingTime { get; set; }
        public int BatchesProcessed { get; set; }
        public TimeSpan AverageBatchProcessingTime { get; set; }
        public int DatabaseInserts { get; set; }
        public int DatabaseUpdates { get; set; }
        public int StorageOperations { get; set; }
        public int CacheInvalidations { get; set; }
        public double DatabaseOperationsPerSecond { get; set; }
        public double CacheHitRate { get; set; }
        public int RetryAttempts { get; set; }
        public int RecoveredErrors { get; set; }
        
        /// <summary>
        /// Update performance metrics
        /// </summary>
        public void UpdateMetrics(TimeSpan totalDuration, int totalItems, long totalBytes)
        {
            if (totalDuration.TotalSeconds > 0)
            {
                ItemsPerSecond = totalItems / totalDuration.TotalSeconds;
                MegabytesPerSecond = (totalBytes / 1024.0 / 1024.0) / totalDuration.TotalSeconds;
                DatabaseOperationsPerSecond = (DatabaseInserts + DatabaseUpdates) / totalDuration.TotalSeconds;
            }
            
            if (totalItems > 0)
            {
                AverageItemProcessingTime = TimeSpan.FromMilliseconds(totalDuration.TotalMilliseconds / totalItems);
            }
        }
    }
    
    /// <summary>
    /// Progress tracking for ongoing migrations
    /// </summary>
    public class MigrationProgress
    {
        public string MigrationId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int CurrentItem { get; set; }
        public int TotalItems { get; set; }
        public string? CurrentItemName { get; set; }
        public string? CurrentOperation { get; set; }
        public double ProgressPercentage { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan EstimatedTimeRemaining { get; set; }
        public double ItemsPerSecond { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public int SkippedCount { get; set; }
        public List<string> RecentErrors { get; set; } = new();
        public List<string> RecentWarnings { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Update progress with current item
        /// </summary>
        public void UpdateProgress(int currentItem, string? itemName = null, string? operation = null)
        {
            CurrentItem = currentItem;
            CurrentItemName = itemName;
            CurrentOperation = operation;
            ProgressPercentage = TotalItems > 0 ? (double)currentItem / TotalItems * 100 : 0;
            LastUpdated = DateTime.UtcNow;
        }
        
        /// <summary>
        /// Add recent error
        /// </summary>
        public void AddRecentError(string error)
        {
            RecentErrors.Add($"{DateTime.UtcNow:HH:mm:ss}: {error}");
            if (RecentErrors.Count > 10)
            {
                RecentErrors.RemoveAt(0);
            }
        }
        
        /// <summary>
        /// Add recent warning
        /// </summary>
        public void AddRecentWarning(string warning)
        {
            RecentWarnings.Add($"{DateTime.UtcNow:HH:mm:ss}: {warning}");
            if (RecentWarnings.Count > 10)
            {
                RecentWarnings.RemoveAt(0);
            }
        }
    }
    
    /// <summary>
    /// Validation result for migration data
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public int ValidatedItems { get; set; }
        public int InvalidItems { get; set; }
        public Dictionary<string, int> ValidationIssues { get; set; } = new();
        
        public void AddError(string error)
        {
            Errors.Add(error);
            InvalidItems++;
        }
        
        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
        
        public void AddValidItem()
        {
            ValidatedItems++;
        }
        
        public void AddValidationIssue(string issue)
        {
            ValidationIssues[issue] = ValidationIssues.GetValueOrDefault(issue, 0) + 1;
        }
    }
}
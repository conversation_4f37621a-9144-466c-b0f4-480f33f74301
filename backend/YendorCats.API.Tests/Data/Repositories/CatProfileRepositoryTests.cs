using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using YendorCats.API.Data;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Data.Repositories
{
    [TestClass]
    public class CatProfileRepositoryTests
    {
        private AppDbContext _context;
        private CatProfileRepository _repository;
        private List<CatProfile> _testProfiles;

        [TestInitialize]
        public void Setup()
        {
            // Create in-memory database for testing
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new AppDbContext(options);
            _repository = new CatProfileRepository(_context);

            // Seed test data
            _testProfiles = new List<CatProfile>
            {
                new CatProfile
                {
                    Id = 1,
                    Name = "Fluffy",
                    RegisteredName = "CH Test Cattery's Fluffy",
                    CatId = "fluffy-001",
                    Breed = "Maine Coon",
                    Gender = "M",
                    DateOfBirth = new DateTime(2020, 6, 15),
                    Color = "Brown Tabby",
                    EyeColor = "Green",
                    Weight = 12.5f,
                    Status = "Active",
                    BreedingStatus = "Breeding",
                    BloodlineType = "Foundation",
                    Bloodline = "Test Bloodline A",
                    IsActive = true,
                    IsPublic = true,
                    IsBreeding = true,
                    IsStudService = true,
                    IsRetired = false,
                    ViewCount = 100,
                    LikeCount = 50,
                    OffspringCount = 15,
                    CreatedAt = DateTime.UtcNow.AddDays(-365),
                    UpdatedAt = DateTime.UtcNow.AddDays(-30)
                },
                new CatProfile
                {
                    Id = 2,
                    Name = "Mittens",
                    RegisteredName = "GCH Test Cattery's Mittens",
                    CatId = "mittens-002",
                    Breed = "Maine Coon",
                    Gender = "F",
                    DateOfBirth = new DateTime(2021, 3, 20),
                    Color = "Silver Tabby",
                    EyeColor = "Blue",
                    Weight = 9.8f,
                    Status = "Active",
                    BreedingStatus = "Breeding",
                    BloodlineType = "Champion",
                    Bloodline = "Test Bloodline B",
                    IsActive = true,
                    IsPublic = true,
                    IsBreeding = true,
                    IsStudService = false,
                    IsRetired = false,
                    ViewCount = 75,
                    LikeCount = 35,
                    OffspringCount = 8,
                    CreatedAt = DateTime.UtcNow.AddDays(-300),
                    UpdatedAt = DateTime.UtcNow.AddDays(-15)
                },
                new CatProfile
                {
                    Id = 3,
                    Name = "Whiskers",
                    RegisteredName = "Test Cattery's Whiskers",
                    CatId = "whiskers-003",
                    Breed = "Maine Coon",
                    Gender = "F",
                    DateOfBirth = new DateTime(2019, 8, 10),
                    Color = "Tortoiseshell",
                    EyeColor = "Amber",
                    Weight = 11.2f,
                    Status = "Retired",
                    BreedingStatus = "Retired",
                    BloodlineType = "Foundation",
                    Bloodline = "Test Bloodline A",
                    IsActive = false,
                    IsPublic = true,
                    IsBreeding = false,
                    IsStudService = false,
                    IsRetired = true,
                    ViewCount = 200,
                    LikeCount = 100,
                    OffspringCount = 25,
                    CreatedAt = DateTime.UtcNow.AddDays(-500),
                    UpdatedAt = DateTime.UtcNow.AddDays(-60)
                },
                new CatProfile
                {
                    Id = 4,
                    Name = "Shadow",
                    RegisteredName = "Test Cattery's Shadow",
                    CatId = "shadow-004",
                    Breed = "Maine Coon",
                    Gender = "M",
                    DateOfBirth = new DateTime(2022, 11, 5),
                    Color = "Black",
                    EyeColor = "Gold",
                    Weight = 8.5f,
                    Status = "Active",
                    BreedingStatus = "Pet",
                    BloodlineType = "Pet",
                    Bloodline = "Test Bloodline C",
                    IsActive = true,
                    IsPublic = false,
                    IsBreeding = false,
                    IsStudService = false,
                    IsRetired = false,
                    ViewCount = 25,
                    LikeCount = 10,
                    OffspringCount = 0,
                    CreatedAt = DateTime.UtcNow.AddDays(-50),
                    UpdatedAt = DateTime.UtcNow.AddDays(-5)
                }
            };

            _context.CatProfiles.AddRange(_testProfiles);
            _context.SaveChanges();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _context.Dispose();
        }

        [TestMethod]
        public async Task GetAllAsync_WithDefaults_ShouldReturnActivePublicProfiles()
        {
            // Act
            var result = await _repository.GetAllAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // Only active and public profiles
            Assert.AreEqual(2, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsActive && p.IsPublic));
        }

        [TestMethod]
        public async Task GetAllAsync_WithActiveOnlyFalse_ShouldReturnAllPublicProfiles()
        {
            // Act
            var result = await _repository.GetAllAsync(1, 10, activeOnly: false);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(3, result.TotalCount); // All public profiles (active and inactive)
            Assert.AreEqual(3, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsPublic));
        }

        [TestMethod]
        public async Task GetAllAsync_WithPublicOnlyFalse_ShouldReturnAllActiveProfiles()
        {
            // Act
            var result = await _repository.GetAllAsync(1, 10, publicOnly: false);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(3, result.TotalCount); // All active profiles (public and private)
            Assert.AreEqual(3, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsActive));
        }

        [TestMethod]
        public async Task GetByIdAsync_WithValidId_ShouldReturnProfile()
        {
            // Act
            var result = await _repository.GetByIdAsync(1);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Id);
            Assert.AreEqual("Fluffy", result.Name);
        }

        [TestMethod]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetByIdAsync(999);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetByCatIdAsync_WithValidCatId_ShouldReturnProfile()
        {
            // Act
            var result = await _repository.GetByCatIdAsync("fluffy-001");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("fluffy-001", result.CatId);
            Assert.AreEqual("Fluffy", result.Name);
        }

        [TestMethod]
        public async Task GetByCatIdAsync_WithInvalidCatId_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetByCatIdAsync("nonexistent");

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetBreedingCatsAsync_ShouldReturnBreedingProfiles()
        {
            // Act
            var result = await _repository.GetBreedingCatsAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // Two breeding cats
            Assert.AreEqual(2, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsBreeding && p.IsActive && p.IsPublic));
        }

        [TestMethod]
        public async Task GetStudsAsync_ShouldReturnStudProfiles()
        {
            // Act
            var result = await _repository.GetStudsAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // One stud service cat
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsStudService && p.Gender == "M"));
        }

        [TestMethod]
        public async Task GetQueensAsync_ShouldReturnQueenProfiles()
        {
            // Act
            var result = await _repository.GetQueensAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // One queen (breeding female)
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsBreeding && p.Gender == "F"));
        }

        [TestMethod]
        public async Task GetByGenderAsync_WithMale_ShouldReturnMaleProfiles()
        {
            // Act
            var result = await _repository.GetByGenderAsync("M", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // One active public male
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.Gender == "M"));
        }

        [TestMethod]
        public async Task GetByGenderAsync_WithFemale_ShouldReturnFemaleProfiles()
        {
            // Act
            var result = await _repository.GetByGenderAsync("F", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // One active public female
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.Gender == "F"));
        }

        [TestMethod]
        public async Task GetByBloodlineAsync_WithValidBloodline_ShouldReturnProfiles()
        {
            // Act
            var result = await _repository.GetByBloodlineAsync("Test Bloodline A", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // One active public profile with this bloodline
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.Bloodline == "Test Bloodline A"));
        }

        [TestMethod]
        public async Task GetByBloodlineAsync_WithInvalidBloodline_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.GetByBloodlineAsync("Nonexistent Bloodline", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount);
            Assert.AreEqual(0, result.Items.Count);
        }

        [TestMethod]
        public async Task GetByBreedingStatusAsync_WithBreeding_ShouldReturnBreedingProfiles()
        {
            // Act
            var result = await _repository.GetByBreedingStatusAsync("Breeding", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // Two breeding profiles
            Assert.AreEqual(2, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.BreedingStatus == "Breeding"));
        }

        [TestMethod]
        public async Task GetByBreedingStatusAsync_WithPet_ShouldReturnPetProfiles()
        {
            // Act
            var result = await _repository.GetByBreedingStatusAsync("Pet", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount); // No active public pets
            Assert.AreEqual(0, result.Items.Count);
        }

        [TestMethod]
        public async Task GetAvailableForBreedingAsync_ShouldReturnAvailableProfiles()
        {
            // Act
            var result = await _repository.GetAvailableForBreedingAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // Two available breeding cats
            Assert.AreEqual(2, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsBreeding && p.IsActive && !p.IsRetired));
        }

        [TestMethod]
        public async Task GetRetiredAsync_ShouldReturnRetiredProfiles()
        {
            // Act
            var result = await _repository.GetRetiredAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // One retired public profile
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.IsRetired && p.IsPublic));
        }

        [TestMethod]
        public async Task GetPedigreeAsync_WithValidCatId_ShouldReturnPedigreeInfo()
        {
            // Act
            var result = await _repository.GetPedigreeAsync("fluffy-001");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("fluffy-001", result.CatId);
            Assert.AreEqual("Test Bloodline A", result.Bloodline);
            Assert.AreEqual("Foundation", result.BloodlineType);
        }

        [TestMethod]
        public async Task GetPedigreeAsync_WithInvalidCatId_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetPedigreeAsync("nonexistent");

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetOffspringAsync_WithValidCatId_ShouldReturnOffspringInfo()
        {
            // Act
            var result = await _repository.GetOffspringAsync("fluffy-001", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            // Note: This would require offspring relationship data to be properly tested
            // For now, we're just verifying the method doesn't throw an exception
        }

        [TestMethod]
        public async Task CreateAsync_WithValidProfile_ShouldCreateSuccessfully()
        {
            // Arrange
            var newProfile = new CatProfile
            {
                Name = "Luna",
                CatId = "luna-005",
                Breed = "Maine Coon",
                Gender = "F",
                DateOfBirth = new DateTime(2023, 1, 15),
                Color = "White",
                EyeColor = "Blue",
                Weight = 7.5f,
                Status = "Active",
                BreedingStatus = "Pet",
                BloodlineType = "Pet",
                IsActive = true,
                IsPublic = true
            };

            // Act
            var result = await _repository.CreateAsync(newProfile);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Id > 0);
            Assert.AreEqual("Luna", result.Name);

            // Verify it was saved to database
            var savedProfile = await _context.CatProfiles.FindAsync(result.Id);
            Assert.IsNotNull(savedProfile);
            Assert.AreEqual("Luna", savedProfile.Name);
        }

        [TestMethod]
        public async Task UpdateAsync_WithValidProfile_ShouldUpdateSuccessfully()
        {
            // Arrange
            var existingProfile = await _repository.GetByIdAsync(1);
            existingProfile.Name = "Updated Fluffy";
            existingProfile.Weight = 13.0f;

            // Act
            var result = await _repository.UpdateAsync(existingProfile);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Updated Fluffy", result.Name);
            Assert.AreEqual(13.0f, result.Weight);

            // Verify it was updated in database
            var updatedProfile = await _context.CatProfiles.FindAsync(1);
            Assert.IsNotNull(updatedProfile);
            Assert.AreEqual("Updated Fluffy", updatedProfile.Name);
        }

        [TestMethod]
        public async Task DeleteAsync_WithValidId_ShouldDeleteSuccessfully()
        {
            // Act
            var result = await _repository.DeleteAsync(1);

            // Assert
            Assert.IsTrue(result);

            // Verify it was deleted from database
            var deletedProfile = await _context.CatProfiles.FindAsync(1);
            Assert.IsNull(deletedProfile);
        }

        [TestMethod]
        public async Task DeleteAsync_WithInvalidId_ShouldReturnFalse()
        {
            // Act
            var result = await _repository.DeleteAsync(999);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task GetTotalCountAsync_WithDefaults_ShouldReturnCorrectCount()
        {
            // Act
            var result = await _repository.GetTotalCountAsync();

            // Assert
            Assert.AreEqual(2, result); // Only active and public profiles
        }

        [TestMethod]
        public async Task GetTotalCountAsync_WithActiveOnlyFalse_ShouldReturnCorrectCount()
        {
            // Act
            var result = await _repository.GetTotalCountAsync(activeOnly: false);

            // Assert
            Assert.AreEqual(3, result); // All public profiles
        }

        [TestMethod]
        public async Task GetBreedingStatsAsync_ShouldReturnCorrectStats()
        {
            // Act
            var result = await _repository.GetBreedingStatsAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalBreedingCats);
            Assert.AreEqual(1, result.TotalStuds);
            Assert.AreEqual(1, result.TotalQueens);
            Assert.AreEqual(1, result.TotalRetired);
        }

        [TestMethod]
        public async Task SearchAsync_WithValidQuery_ShouldReturnMatchingProfiles()
        {
            // Act
            var result = await _repository.SearchAsync("fluffy", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount);
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => 
                p.Name.Contains("fluffy", StringComparison.OrdinalIgnoreCase) ||
                p.RegisteredName != null && p.RegisteredName.Contains("fluffy", StringComparison.OrdinalIgnoreCase)));
        }

        [TestMethod]
        public async Task SearchAsync_WithEmptyQuery_ShouldReturnAllProfiles()
        {
            // Act
            var result = await _repository.SearchAsync("", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // All active public profiles
            Assert.AreEqual(2, result.Items.Count);
        }

        [TestMethod]
        public async Task SearchAsync_WithNonexistentQuery_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.SearchAsync("nonexistent", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount);
            Assert.AreEqual(0, result.Items.Count);
        }

        [TestMethod]
        public async Task GetByAgeRangeAsync_WithValidRange_ShouldReturnProfiles()
        {
            // Act
            var result = await _repository.GetByAgeRangeAsync(2, 4, 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // Two profiles in this age range
            Assert.AreEqual(2, result.Items.Count);
            Assert.IsTrue(result.Items.All(p => p.Age >= 2 && p.Age <= 4));
        }

        [TestMethod]
        public async Task GetByAgeRangeAsync_WithInvalidRange_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.GetByAgeRangeAsync(10, 15, 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount);
            Assert.AreEqual(0, result.Items.Count);
        }

        [TestMethod]
        public async Task GetMostViewedAsync_ShouldReturnMostViewedProfiles()
        {
            // Act
            var result = await _repository.GetMostViewedAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(2, result.Items.Count);
            
            // Should be ordered by ViewCount descending
            Assert.IsTrue(result.Items[0].ViewCount >= result.Items[1].ViewCount);
        }

        [TestMethod]
        public async Task GetMostLikedAsync_ShouldReturnMostLikedProfiles()
        {
            // Act
            var result = await _repository.GetMostLikedAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(2, result.Items.Count);
            
            // Should be ordered by LikeCount descending
            Assert.IsTrue(result.Items[0].LikeCount >= result.Items[1].LikeCount);
        }

        [TestMethod]
        public async Task GetMostProductiveAsync_ShouldReturnMostProductiveProfiles()
        {
            // Act
            var result = await _repository.GetMostProductiveAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(2, result.Items.Count);
            
            // Should be ordered by OffspringCount descending
            Assert.IsTrue(result.Items[0].OffspringCount >= result.Items[1].OffspringCount);
        }

        [TestMethod]
        public async Task GetRecentlyAddedAsync_ShouldReturnRecentProfiles()
        {
            // Act
            var result = await _repository.GetRecentlyAddedAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(2, result.Items.Count);
            
            // Should be ordered by CreatedAt descending
            Assert.IsTrue(result.Items[0].CreatedAt >= result.Items[1].CreatedAt);
        }

        [TestMethod]
        public async Task GetRecentlyUpdatedAsync_ShouldReturnRecentlyUpdatedProfiles()
        {
            // Act
            var result = await _repository.GetRecentlyUpdatedAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(2, result.Items.Count);
            
            // Should be ordered by UpdatedAt descending
            Assert.IsTrue(result.Items[0].UpdatedAt >= result.Items[1].UpdatedAt);
        }
    }
}
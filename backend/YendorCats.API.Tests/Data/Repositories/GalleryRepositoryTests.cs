using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using YendorCats.API.Data;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Data.Repositories
{
    [TestClass]
    public class GalleryRepositoryTests
    {
        private AppDbContext _context;
        private GalleryRepository _repository;
        private List<CatGalleryImage> _testImages;

        [TestInitialize]
        public void Setup()
        {
            // Create in-memory database for testing
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new AppDbContext(options);
            _repository = new GalleryRepository(_context);

            // Seed test data
            _testImages = new List<CatGalleryImage>
            {
                new CatGalleryImage
                {
                    Id = 1,
                    Filename = "test1.jpg",
                    StorageKey = "cats/test1.jpg",
                    OriginalFileName = "test1.jpg",
                    CatId = "cat1",
                    CatName = "Fluffy",
                    Category = "gallery",
                    IsActive = true,
                    IsPublic = true,
                    IsFeatured = true,
                    ViewCount = 100,
                    LikeCount = 50,
                    Tags = "fluffy,cute",
                    DateTaken = DateTime.UtcNow.AddDays(-30),
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddDays(-30),
                    StorageProvider = "S3",
                    StorageBucketName = "test-bucket"
                },
                new CatGalleryImage
                {
                    Id = 2,
                    Filename = "test2.jpg",
                    StorageKey = "cats/test2.jpg",
                    OriginalFileName = "test2.jpg",
                    CatId = "cat2",
                    CatName = "Mittens",
                    Category = "studs",
                    IsActive = true,
                    IsPublic = true,
                    IsFeatured = false,
                    ViewCount = 75,
                    LikeCount = 25,
                    Tags = "mittens,playful",
                    DateTaken = DateTime.UtcNow.AddDays(-15),
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    UpdatedAt = DateTime.UtcNow.AddDays(-15),
                    StorageProvider = "B2",
                    StorageBucketName = "test-bucket"
                },
                new CatGalleryImage
                {
                    Id = 3,
                    Filename = "test3.jpg",
                    StorageKey = "cats/test3.jpg",
                    OriginalFileName = "test3.jpg",
                    CatId = "cat1",
                    CatName = "Fluffy",
                    Category = "gallery",
                    IsActive = false,
                    IsPublic = true,
                    IsFeatured = false,
                    ViewCount = 25,
                    LikeCount = 10,
                    Tags = "fluffy,sleeping",
                    DateTaken = DateTime.UtcNow.AddDays(-7),
                    CreatedAt = DateTime.UtcNow.AddDays(-7),
                    UpdatedAt = DateTime.UtcNow.AddDays(-7),
                    StorageProvider = "S3",
                    StorageBucketName = "test-bucket"
                },
                new CatGalleryImage
                {
                    Id = 4,
                    Filename = "test4.jpg",
                    StorageKey = "cats/test4.jpg",
                    OriginalFileName = "test4.jpg",
                    CatId = "cat3",
                    CatName = "Whiskers",
                    Category = "queens",
                    IsActive = true,
                    IsPublic = false,
                    IsFeatured = false,
                    ViewCount = 150,
                    LikeCount = 75,
                    Tags = "whiskers,elegant",
                    DateTaken = DateTime.UtcNow.AddDays(-5),
                    CreatedAt = DateTime.UtcNow.AddDays(-5),
                    UpdatedAt = DateTime.UtcNow.AddDays(-5),
                    StorageProvider = "S3",
                    StorageBucketName = "test-bucket"
                }
            };

            _context.CatGalleryImages.AddRange(_testImages);
            _context.SaveChanges();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _context.Dispose();
        }

        [TestMethod]
        public async Task GetAllAsync_WithDefaults_ShouldReturnActivePublicImages()
        {
            // Act
            var result = await _repository.GetAllAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // Only active and public images
            Assert.AreEqual(2, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => i.IsActive && i.IsPublic));
        }

        [TestMethod]
        public async Task GetAllAsync_WithActiveOnlyFalse_ShouldReturnAllPublicImages()
        {
            // Act
            var result = await _repository.GetAllAsync(1, 10, activeOnly: false);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(3, result.TotalCount); // All public images (active and inactive)
            Assert.AreEqual(3, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => i.IsPublic));
        }

        [TestMethod]
        public async Task GetAllAsync_WithPublicOnlyFalse_ShouldReturnAllActiveImages()
        {
            // Act
            var result = await _repository.GetAllAsync(1, 10, publicOnly: false);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(3, result.TotalCount); // All active images (public and private)
            Assert.AreEqual(3, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => i.IsActive));
        }

        [TestMethod]
        public async Task GetAllAsync_WithPagination_ShouldReturnCorrectPage()
        {
            // Act
            var result = await _repository.GetAllAsync(1, 1);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(1, result.Items.Count);
            Assert.AreEqual(1, result.CurrentPage);
            Assert.AreEqual(2, result.TotalPages);
        }

        [TestMethod]
        public async Task GetByIdAsync_WithValidId_ShouldReturnImage()
        {
            // Act
            var result = await _repository.GetByIdAsync(1);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Id);
            Assert.AreEqual("test1.jpg", result.Filename);
        }

        [TestMethod]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetByIdAsync(999);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetByFilenameAsync_WithValidFilename_ShouldReturnImage()
        {
            // Act
            var result = await _repository.GetByFilenameAsync("test1.jpg");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("test1.jpg", result.Filename);
            Assert.AreEqual(1, result.Id);
        }

        [TestMethod]
        public async Task GetByFilenameAsync_WithInvalidFilename_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetByFilenameAsync("nonexistent.jpg");

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetByCatIdAsync_WithValidCatId_ShouldReturnImages()
        {
            // Act
            var result = await _repository.GetByCatIdAsync("cat1", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // Only active public images for cat1
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => i.CatId == "cat1"));
        }

        [TestMethod]
        public async Task GetByCatIdAsync_WithInvalidCatId_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.GetByCatIdAsync("nonexistent", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount);
            Assert.AreEqual(0, result.Items.Count);
        }

        [TestMethod]
        public async Task GetByStorageKeyAsync_WithValidKey_ShouldReturnImage()
        {
            // Act
            var result = await _repository.GetByStorageKeyAsync("cats/test1.jpg");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("cats/test1.jpg", result.StorageKey);
            Assert.AreEqual(1, result.Id);
        }

        [TestMethod]
        public async Task GetByStorageKeyAsync_WithInvalidKey_ShouldReturnNull()
        {
            // Act
            var result = await _repository.GetByStorageKeyAsync("cats/nonexistent.jpg");

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task CreateAsync_WithValidImage_ShouldCreateSuccessfully()
        {
            // Arrange
            var newImage = new CatGalleryImage
            {
                Filename = "new-test.jpg",
                StorageKey = "cats/new-test.jpg",
                OriginalFileName = "new-test.jpg",
                CatId = "cat4",
                CatName = "NewCat",
                Category = "gallery",
                IsActive = true,
                IsPublic = true,
                StorageProvider = "S3",
                StorageBucketName = "test-bucket"
            };

            // Act
            var result = await _repository.CreateAsync(newImage);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Id > 0);
            Assert.AreEqual("new-test.jpg", result.Filename);

            // Verify it was saved to database
            var savedImage = await _context.CatGalleryImages.FindAsync(result.Id);
            Assert.IsNotNull(savedImage);
            Assert.AreEqual("new-test.jpg", savedImage.Filename);
        }

        [TestMethod]
        public async Task UpdateAsync_WithValidImage_ShouldUpdateSuccessfully()
        {
            // Arrange
            var existingImage = await _repository.GetByIdAsync(1);
            existingImage.CatName = "Updated Fluffy";
            existingImage.Description = "Updated description";

            // Act
            var result = await _repository.UpdateAsync(existingImage);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Updated Fluffy", result.CatName);
            Assert.AreEqual("Updated description", result.Description);

            // Verify it was updated in database
            var updatedImage = await _context.CatGalleryImages.FindAsync(1);
            Assert.IsNotNull(updatedImage);
            Assert.AreEqual("Updated Fluffy", updatedImage.CatName);
        }

        [TestMethod]
        public async Task DeleteAsync_WithValidId_ShouldDeleteSuccessfully()
        {
            // Act
            var result = await _repository.DeleteAsync(1);

            // Assert
            Assert.IsTrue(result);

            // Verify it was deleted from database
            var deletedImage = await _context.CatGalleryImages.FindAsync(1);
            Assert.IsNull(deletedImage);
        }

        [TestMethod]
        public async Task DeleteAsync_WithInvalidId_ShouldReturnFalse()
        {
            // Act
            var result = await _repository.DeleteAsync(999);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task GetTotalCountAsync_WithDefaults_ShouldReturnCorrectCount()
        {
            // Act
            var result = await _repository.GetTotalCountAsync();

            // Assert
            Assert.AreEqual(2, result); // Only active and public images
        }

        [TestMethod]
        public async Task GetTotalCountAsync_WithActiveOnlyFalse_ShouldReturnCorrectCount()
        {
            // Act
            var result = await _repository.GetTotalCountAsync(activeOnly: false);

            // Assert
            Assert.AreEqual(3, result); // All public images
        }

        [TestMethod]
        public async Task GetByCategoryAsync_WithValidCategory_ShouldReturnImages()
        {
            // Act
            var result = await _repository.GetByCategoryAsync("gallery", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // Only active public gallery images
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => i.Category == "gallery"));
        }

        [TestMethod]
        public async Task GetByCategoryAsync_WithInvalidCategory_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.GetByCategoryAsync("nonexistent", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount);
            Assert.AreEqual(0, result.Items.Count);
        }

        [TestMethod]
        public async Task GetFeaturedAsync_ShouldReturnFeaturedImages()
        {
            // Act
            var result = await _repository.GetFeaturedAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // Only one featured image
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => i.IsFeatured));
        }

        [TestMethod]
        public async Task GetRecentAsync_ShouldReturnRecentImages()
        {
            // Act
            var result = await _repository.GetRecentAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(2, result.Items.Count);
            
            // Should be ordered by DateTaken descending
            Assert.IsTrue(result.Items[0].DateTaken >= result.Items[1].DateTaken);
        }

        [TestMethod]
        public async Task GetPopularAsync_ShouldReturnPopularImages()
        {
            // Act
            var result = await _repository.GetPopularAsync(1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount);
            Assert.AreEqual(2, result.Items.Count);
            
            // Should be ordered by ViewCount descending
            Assert.IsTrue(result.Items[0].ViewCount >= result.Items[1].ViewCount);
        }

        [TestMethod]
        public async Task GetByTagsAsync_WithValidTags_ShouldReturnImages()
        {
            // Act
            var result = await _repository.GetByTagsAsync(new[] { "fluffy" }, 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount); // Only active public images with "fluffy" tag
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => i.Tags.Contains("fluffy")));
        }

        [TestMethod]
        public async Task GetByTagsAsync_WithMultipleTags_ShouldReturnImagesWithAnyTag()
        {
            // Act
            var result = await _repository.GetByTagsAsync(new[] { "fluffy", "playful" }, 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // Images with either "fluffy" or "playful" tag
            Assert.AreEqual(2, result.Items.Count);
        }

        [TestMethod]
        public async Task GetByTagsAsync_WithNonexistentTags_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.GetByTagsAsync(new[] { "nonexistent" }, 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount);
            Assert.AreEqual(0, result.Items.Count);
        }

        [TestMethod]
        public async Task BulkUpdateAsync_WithValidImages_ShouldUpdateAll()
        {
            // Arrange
            var imagesToUpdate = await _repository.GetAllAsync(1, 10);
            foreach (var image in imagesToUpdate.Items)
            {
                image.Description = "Bulk updated description";
            }

            // Act
            var result = await _repository.BulkUpdateAsync(imagesToUpdate.Items);

            // Assert
            Assert.AreEqual(2, result);

            // Verify all images were updated
            var updatedImages = await _repository.GetAllAsync(1, 10);
            Assert.IsTrue(updatedImages.Items.All(i => i.Description == "Bulk updated description"));
        }

        [TestMethod]
        public async Task GetMigrationCandidatesAsync_WithS3Images_ShouldReturnCandidates()
        {
            // Act
            var result = await _repository.GetMigrationCandidatesAsync("S3", 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count); // Two S3 images in test data
            Assert.IsTrue(result.All(i => i.StorageProvider == "S3"));
        }

        [TestMethod]
        public async Task GetMigrationCandidatesAsync_WithB2Images_ShouldReturnCandidates()
        {
            // Act
            var result = await _repository.GetMigrationCandidatesAsync("B2", 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Count); // One B2 image in test data
            Assert.IsTrue(result.All(i => i.StorageProvider == "B2"));
        }

        [TestMethod]
        public async Task GetMigrationCandidatesAsync_WithInvalidProvider_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.GetMigrationCandidatesAsync("Invalid", 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count);
        }

        [TestMethod]
        public async Task GetStorageProviderStatsAsync_ShouldReturnCorrectStats()
        {
            // Act
            var result = await _repository.GetStorageProviderStatsAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count);
            
            var s3Stats = result.FirstOrDefault(s => s.Provider == "S3");
            Assert.IsNotNull(s3Stats);
            Assert.AreEqual(3, s3Stats.Count); // 3 S3 images in test data
            
            var b2Stats = result.FirstOrDefault(s => s.Provider == "B2");
            Assert.IsNotNull(b2Stats);
            Assert.AreEqual(1, b2Stats.Count); // 1 B2 image in test data
        }

        [TestMethod]
        public async Task SearchAsync_WithValidQuery_ShouldReturnMatchingImages()
        {
            // Act
            var result = await _repository.SearchAsync("fluffy", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.TotalCount);
            Assert.AreEqual(1, result.Items.Count);
            Assert.IsTrue(result.Items.All(i => 
                i.CatName.Contains("fluffy", StringComparison.OrdinalIgnoreCase) ||
                i.Tags.Contains("fluffy", StringComparison.OrdinalIgnoreCase) ||
                i.Description != null && i.Description.Contains("fluffy", StringComparison.OrdinalIgnoreCase)));
        }

        [TestMethod]
        public async Task SearchAsync_WithEmptyQuery_ShouldReturnAllImages()
        {
            // Act
            var result = await _repository.SearchAsync("", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.TotalCount); // All active public images
            Assert.AreEqual(2, result.Items.Count);
        }

        [TestMethod]
        public async Task SearchAsync_WithNonexistentQuery_ShouldReturnEmptyResult()
        {
            // Act
            var result = await _repository.SearchAsync("nonexistent", 1, 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.TotalCount);
            Assert.AreEqual(0, result.Items.Count);
        }
    }
}
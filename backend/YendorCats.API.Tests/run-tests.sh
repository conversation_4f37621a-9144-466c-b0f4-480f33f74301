#!/bin/bash

# <PERSON>ript to run all S3 metadata tests

echo "=========================================="
echo "Running S3 Metadata Tests"
echo "=========================================="

cd "$(dirname "$0")"

# Build the test project
echo "Building test project..."
dotnet build

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"
echo ""

# Run all tests
echo "Running all tests..."
dotnet test --verbosity normal --collect:"XPlat Code Coverage"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ All tests passed!"
else
    echo ""
    echo "❌ Some tests failed!"
    exit 1
fi

echo ""
echo "=========================================="
echo "Test Results Summary"
echo "=========================================="

# Run specific test categories
echo "Running S3StorageService tests..."
dotnet test --filter "FullyQualifiedName~S3StorageServiceTests" --verbosity normal

echo ""
echo "Running S3MetadataController tests..."
dotnet test --filter "FullyQualifiedName~S3MetadataControllerTests" --verbosity normal

echo ""
echo "Running CatImageMetadata tests..."
dotnet test --filter "FullyQualifiedName~CatImageMetadataTests" --verbosity normal

echo ""
echo "=========================================="
echo "Test Coverage Report"
echo "=========================================="
echo "Test coverage files can be found in TestResults/ directory"
echo "To generate HTML coverage report:"
echo "dotnet tool install -g dotnet-reportgenerator-globaltool"
echo "reportgenerator -reports:**/coverage.cobertura.xml -targetdir:coveragereport -reporttypes:Html"
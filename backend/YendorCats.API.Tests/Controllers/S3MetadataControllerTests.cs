using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using Amazon.S3;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using YendorCats.API.Controllers;
using YendorCats.API.Services;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Controllers
{
    public class S3MetadataControllerTests
    {
        private readonly Mock<IS3StorageService> _mockS3StorageService;
        private readonly Mock<ILogger<S3MetadataController>> _mockLogger;
        private readonly S3MetadataController _controller;

        public S3MetadataControllerTests()
        {
            _mockS3StorageService = new Mock<IS3StorageService>();
            _mockLogger = new Mock<ILogger<S3MetadataController>>();
            _controller = new S3MetadataController(_mockS3StorageService.Object, _mockLogger.Object);

            // Setup mock HttpContext with admin user
            var httpContext = new DefaultHttpContext();
            httpContext.Items["AdminUser"] = new AdminUser 
            { 
                Username = "testadmin", 
                Role = "Admin" 
            };
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };
        }

        [Fact]
        public async Task UpdateMetadata_ValidRequest_ReturnsOkResult()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "test-file.jpg",
                CatName = "Fluffy",
                Age = "2",
                Gender = "F",
                Breed = "Persian"
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateMetadata(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync(request.S3Key, It.IsAny<Dictionary<string, string>>()), Times.Once);
        }

        [Fact]
        public async Task UpdateMetadata_EmptyS3Key_ReturnsBadRequest()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "",
                CatName = "Fluffy"
            };

            // Act
            var result = await _controller.UpdateMetadata(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var response = badRequestResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }

        [Fact]
        public async Task UpdateMetadata_S3NotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "nonexistent-file.jpg",
                CatName = "Fluffy"
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new AmazonS3Exception("Not Found") { StatusCode = HttpStatusCode.NotFound });

            // Act
            var result = await _controller.UpdateMetadata(request);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var response = notFoundResult.Value as dynamic;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task UpdateMetadata_S3AccessDenied_ReturnsForbidden()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "restricted-file.jpg",
                CatName = "Fluffy"
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new AmazonS3Exception("Access Denied") { StatusCode = HttpStatusCode.Forbidden });

            // Act
            var result = await _controller.UpdateMetadata(request);

            // Assert
            var forbiddenResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(403, forbiddenResult.StatusCode);
        }

        [Fact]
        public async Task UpdateMetadata_TimeoutException_ReturnsTimeoutStatus()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "slow-file.jpg",
                CatName = "Fluffy"
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new TimeoutException("Operation timed out"));

            // Act
            var result = await _controller.UpdateMetadata(request);

            // Assert
            var timeoutResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(408, timeoutResult.StatusCode);
        }

        [Fact]
        public async Task GetMetadata_ValidS3Key_ReturnsOkWithMetadata()
        {
            // Arrange
            var s3Key = "test-file.jpg";
            var expectedMetadata = new Dictionary<string, string>
            {
                ["cat-name"] = "Fluffy",
                ["cat-age"] = "2",
                ["cat-breed"] = "Persian"
            };

            _mockS3StorageService.Setup(s => s.GetObjectMetadataAsync(s3Key))
                .ReturnsAsync(expectedMetadata);

            // Act
            var result = await _controller.GetMetadata(s3Key);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.GetObjectMetadataAsync(s3Key), Times.Once);
        }

        [Fact]
        public async Task GetMetadata_EmptyS3Key_ReturnsBadRequest()
        {
            // Arrange
            var s3Key = "";

            // Act
            var result = await _controller.GetMetadata(s3Key);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var response = badRequestResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.GetObjectMetadataAsync(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task GetMetadata_S3NotFound_ReturnsNotFound()
        {
            // Arrange
            var s3Key = "nonexistent-file.jpg";

            _mockS3StorageService.Setup(s => s.GetObjectMetadataAsync(s3Key))
                .ThrowsAsync(new AmazonS3Exception("Not Found") { StatusCode = HttpStatusCode.NotFound });

            // Act
            var result = await _controller.GetMetadata(s3Key);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var response = notFoundResult.Value as dynamic;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task BulkUpdateMetadata_ValidRequests_ReturnsOkWithSummary()
        {
            // Arrange
            var requests = new List<S3MetadataUpdateRequest>
            {
                new S3MetadataUpdateRequest { S3Key = "file1.jpg", CatName = "Fluffy" },
                new S3MetadataUpdateRequest { S3Key = "file2.jpg", CatName = "Whiskers" }
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.BulkUpdateMetadata(requests);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Exactly(2));
        }

        [Fact]
        public async Task BulkUpdateMetadata_EmptyRequestList_ReturnsBadRequest()
        {
            // Arrange
            var requests = new List<S3MetadataUpdateRequest>();

            // Act
            var result = await _controller.BulkUpdateMetadata(requests);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var response = badRequestResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }

        [Fact]
        public async Task BulkUpdateMetadata_TooManyRequests_ReturnsBadRequest()
        {
            // Arrange
            var requests = new List<S3MetadataUpdateRequest>();
            for (int i = 0; i < 101; i++) // Exceeds the 100 item limit
            {
                requests.Add(new S3MetadataUpdateRequest { S3Key = $"file{i}.jpg", CatName = "Cat" });
            }

            // Act
            var result = await _controller.BulkUpdateMetadata(requests);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var response = badRequestResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
        }

        [Fact]
        public async Task BulkUpdateMetadata_PartialSuccess_ReturnsOkWithPartialResults()
        {
            // Arrange
            var requests = new List<S3MetadataUpdateRequest>
            {
                new S3MetadataUpdateRequest { S3Key = "file1.jpg", CatName = "Fluffy" },
                new S3MetadataUpdateRequest { S3Key = "file2.jpg", CatName = "Whiskers" },
                new S3MetadataUpdateRequest { S3Key = "file3.jpg", CatName = "Mittens" }
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync("file1.jpg", It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);
            
            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync("file2.jpg", It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new AmazonS3Exception("Not Found") { StatusCode = HttpStatusCode.NotFound });
            
            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync("file3.jpg", It.IsAny<Dictionary<string, string>>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.BulkUpdateMetadata(requests);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Exactly(3));
        }

        [Fact]
        public async Task BulkUpdateMetadata_WithRetryableErrors_RetriesSuccessfully()
        {
            // Arrange
            var requests = new List<S3MetadataUpdateRequest>
            {
                new S3MetadataUpdateRequest { S3Key = "file1.jpg", CatName = "Fluffy" }
            };

            var callCount = 0;
            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync("file1.jpg", It.IsAny<Dictionary<string, string>>()))
                .Returns(() =>
                {
                    callCount++;
                    if (callCount < 3) // Fail first 2 attempts
                    {
                        throw new AmazonS3Exception("Service Unavailable") { StatusCode = HttpStatusCode.ServiceUnavailable };
                    }
                    return Task.CompletedTask; // Succeed on 3rd attempt
                });

            // Act
            var result = await _controller.BulkUpdateMetadata(requests);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value as dynamic;
            Assert.NotNull(response);
            
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync("file1.jpg", It.IsAny<Dictionary<string, string>>()), Times.Exactly(3));
        }

        [Fact]
        public async Task BulkUpdateMetadata_MaxRetriesExceeded_ReturnsFailedResult()
        {
            // Arrange
            var requests = new List<S3MetadataUpdateRequest>
            {
                new S3MetadataUpdateRequest { S3Key = "file1.jpg", CatName = "Fluffy" }
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync("file1.jpg", It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new AmazonS3Exception("Service Unavailable") { StatusCode = HttpStatusCode.ServiceUnavailable });

            // Act
            var result = await _controller.BulkUpdateMetadata(requests);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value as dynamic;
            Assert.NotNull(response);
            
            // Should try 3 times per request
            _mockS3StorageService.Verify(s => s.UpdateObjectMetadataAsync("file1.jpg", It.IsAny<Dictionary<string, string>>()), Times.Exactly(3));
        }

        [Fact]
        public async Task BulkUpdateMetadata_CatastrophicFailure_ReturnsInternalServerError()
        {
            // Arrange
            var requests = new List<S3MetadataUpdateRequest>
            {
                new S3MetadataUpdateRequest { S3Key = "file1.jpg", CatName = "Fluffy" }
            };

            _mockS3StorageService.Setup(s => s.UpdateObjectMetadataAsync(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .ThrowsAsync(new InvalidOperationException("Catastrophic system failure"));

            // Act
            var result = await _controller.BulkUpdateMetadata(requests);

            // Assert
            var serverErrorResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, serverErrorResult.StatusCode);
        }

        [Fact]
        public void S3MetadataUpdateRequest_Validation_ValidatesStringLengths()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = new string('a', 1025), // Exceeds 1024 char limit
                CatName = new string('b', 101), // Exceeds 100 char limit
                Description = new string('c', 1001) // Exceeds 1000 char limit
            };

            // Act & Assert
            var validationResults = ValidateModel(request);
            
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("S3 key cannot exceed 1024 characters"));
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Cat name cannot exceed 100 characters"));
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Description cannot exceed 1000 characters"));
        }

        [Fact]
        public void S3MetadataUpdateRequest_Validation_ValidatesRegexPatterns()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "valid-file.jpg",
                Gender = "X", // Invalid gender
                Age = "abc", // Invalid age format
                CatId = "invalid@id", // Invalid cat ID format
                GenerationLevel = "10" // Invalid generation level
            };

            // Act & Assert
            var validationResults = ValidateModel(request);
            
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Gender must be 'M' or 'F'"));
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Age must contain only numbers, dots, dashes, and spaces"));
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Cat ID can only contain letters, numbers, hyphens, and underscores"));
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Generation level must be a number between 1 and 9"));
        }

        [Fact]
        public void S3MetadataUpdateRequest_Validation_ValidatesEnumValues()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "valid-file.jpg",
                BreedingStatus = "invalid-status",
                AvailabilityStatus = "invalid-availability",
                PhotoType = "invalid-type"
            };

            // Act & Assert
            var validationResults = ValidateModel(request);
            
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Breeding status must be one of"));
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Availability status must be one of"));
            Assert.Contains(validationResults, v => v.ErrorMessage.Contains("Photo type must be one of"));
        }

        [Fact]
        public void S3MetadataUpdateRequest_Validation_ValidRequest_PassesValidation()
        {
            // Arrange
            var request = new S3MetadataUpdateRequest
            {
                S3Key = "valid-file.jpg",
                CatName = "Fluffy",
                Age = "2.5",
                Gender = "F",
                Breed = "Persian",
                BreedingStatus = "available-kitten",
                AvailabilityStatus = "available",
                PhotoType = "profile",
                GenerationLevel = "1"
            };

            // Act & Assert
            var validationResults = ValidateModel(request);
            Assert.Empty(validationResults);
        }

        private List<System.ComponentModel.DataAnnotations.ValidationResult> ValidateModel(object model)
        {
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();
            var context = new System.ComponentModel.DataAnnotations.ValidationContext(model, null, null);
            System.ComponentModel.DataAnnotations.Validator.TryValidateObject(model, context, validationResults, true);
            return validationResults;
        }
    }
}
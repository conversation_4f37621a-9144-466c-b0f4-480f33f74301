using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using YendorCats.API.Data;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Services;
using YendorCats.API.Services.Migration;

namespace YendorCats.API.Tests.Integration
{
    [TestClass]
    public class MigrationIntegrationTests
    {
        private ServiceProvider _serviceProvider;
        private AppDbContext _context;
        private IGalleryRepository _galleryRepository;
        private Mock<IS3StorageService> _mockS3StorageService;
        private Mock<IB2StorageService> _mockB2StorageService;
        private IS3ToDbMigrationService _migrationService;
        private MigrationValidator _migrationValidator;
        private MigrationReporter _migrationReporter;
        private SqliteConnection _connection;

        [TestInitialize]
        public async Task Setup()
        {
            // Create in-memory SQLite database
            _connection = new SqliteConnection("DataSource=:memory:");
            _connection.Open();

            // Setup mocks
            _mockS3StorageService = new Mock<IS3StorageService>();
            _mockB2StorageService = new Mock<IB2StorageService>();

            // Configure services
            var services = new ServiceCollection();
            services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite(_connection));
            services.AddScoped<IGalleryRepository, GalleryRepository>();
            services.AddScoped<ICatProfileRepository, CatProfileRepository>();
            services.AddSingleton(_mockS3StorageService.Object);
            services.AddSingleton(_mockB2StorageService.Object);
            services.AddScoped<IS3ToDbMigrationService, S3ToDbMigrationService>();
            services.AddScoped<MigrationValidator>();
            services.AddScoped<MigrationReporter>();
            services.AddLogging();

            _serviceProvider = services.BuildServiceProvider();
            _context = _serviceProvider.GetRequiredService<AppDbContext>();
            _galleryRepository = _serviceProvider.GetRequiredService<IGalleryRepository>();
            _migrationService = _serviceProvider.GetRequiredService<IS3ToDbMigrationService>();
            _migrationValidator = _serviceProvider.GetRequiredService<MigrationValidator>();
            _migrationReporter = _serviceProvider.GetRequiredService<MigrationReporter>();

            // Create database schema
            await _context.Database.EnsureCreatedAsync();

            // Setup S3 mock data
            SetupS3MockData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _serviceProvider?.Dispose();
            _connection?.Close();
            _connection?.Dispose();
        }

        private void SetupS3MockData()
        {
            var s3Objects = new List<S3ObjectInfo>
            {
                new S3ObjectInfo
                {
                    Key = "cats/fluffy/fluffy-001.jpg",
                    Size = 1024576,
                    LastModified = DateTime.UtcNow.AddDays(-30),
                    ETag = "etag1"
                },
                new S3ObjectInfo
                {
                    Key = "cats/mittens/mittens-001.jpg",
                    Size = 2048576,
                    LastModified = DateTime.UtcNow.AddDays(-15),
                    ETag = "etag2"
                },
                new S3ObjectInfo
                {
                    Key = "cats/whiskers/whiskers-001.jpg",
                    Size = 1536576,
                    LastModified = DateTime.UtcNow.AddDays(-7),
                    ETag = "etag3"
                }
            };

            var s3Metadata = new Dictionary<string, CatImageMetadata>
            {
                ["cats/fluffy/fluffy-001.jpg"] = new CatImageMetadata
                {
                    Name = "Fluffy",
                    Gender = "M",
                    FileSize = 1024576,
                    ContentType = "image/jpeg",
                    Width = 1920,
                    Height = 1080,
                    Description = "Beautiful Maine Coon cat",
                    Breed = "Maine Coon",
                    Category = "gallery",
                    Tags = "fluffy,maine-coon,male",
                    DateUploaded = DateTime.UtcNow.AddDays(-30),
                    DateTaken = DateTime.UtcNow.AddDays(-30),
                    CatId = 1,
                    RegisteredName = "CH Test Cattery's Fluffy"
                },
                ["cats/mittens/mittens-001.jpg"] = new CatImageMetadata
                {
                    Name = "Mittens",
                    Gender = "F",
                    FileSize = 2048576,
                    ContentType = "image/jpeg",
                    Width = 1600,
                    Height = 1200,
                    Description = "Playful queen",
                    Breed = "Maine Coon",
                    Category = "queens",
                    Tags = "mittens,maine-coon,female",
                    DateUploaded = DateTime.UtcNow.AddDays(-15),
                    DateTaken = DateTime.UtcNow.AddDays(-15),
                    CatId = 2,
                    RegisteredName = "GCH Test Cattery's Mittens"
                },
                ["cats/whiskers/whiskers-001.jpg"] = new CatImageMetadata
                {
                    Name = "Whiskers",
                    Gender = "M",
                    FileSize = 1536576,
                    ContentType = "image/jpeg",
                    Width = 1800,
                    Height = 1350,
                    Description = "Champion stud",
                    Breed = "Maine Coon",
                    Category = "studs",
                    Tags = "whiskers,maine-coon,male,champion",
                    DateUploaded = DateTime.UtcNow.AddDays(-7),
                    DateTaken = DateTime.UtcNow.AddDays(-7),
                    CatId = 3,
                    RegisteredName = "GCH Test Cattery's Whiskers"
                }
            };

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(s3Objects);

            foreach (var kvp in s3Metadata)
            {
                _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync(kvp.Key))
                    .ReturnsAsync(new S3ObjectMetadata
                    {
                        ContentType = kvp.Value.ContentType,
                        ContentLength = kvp.Value.FileSize,
                        LastModified = kvp.Value.DateUploaded ?? DateTime.UtcNow,
                        ETag = "etag",
                        UserMetadata = new Dictionary<string, string>
                        {
                            ["cat-metadata"] = System.Text.Json.JsonSerializer.Serialize(kvp.Value)
                        }
                    });
            }

            // Setup B2 mock responses
            _mockB2StorageService.Setup(x => x.UploadFileAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<string>()))
                .ReturnsAsync(new B2UploadResult
                {
                    FileId = "b2-file-id",
                    FileName = "test-file.jpg",
                    AccountId = "test-account",
                    BucketId = "test-bucket-id",
                    ContentLength = 1024576,
                    ContentType = "image/jpeg",
                    UploadTimestamp = DateTime.UtcNow
                });
        }

        [TestMethod]
        public async Task CompleteS3ToDbMigration_ShouldMigrateAllData()
        {
            // Arrange
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: false,
                dryRun: false);

            // Act
            await WaitForMigrationCompletion(migrationId, TimeSpan.FromMinutes(2));

            // Assert
            var status = await _migrationService.GetMigrationStatusAsync(migrationId);
            Assert.IsNotNull(status);
            Assert.AreEqual("Completed", status.Status);
            Assert.AreEqual(3, status.ProcessedItems);
            Assert.AreEqual(3, status.SuccessfulItems);
            Assert.AreEqual(0, status.FailedItems);

            // Verify data was migrated to database
            var allImages = await _galleryRepository.GetAllAsync(1, 100, activeOnly: false, publicOnly: false);
            Assert.AreEqual(3, allImages.TotalCount);

            // Verify image details
            var fluffyImage = allImages.Items.FirstOrDefault(i => i.CatName == "Fluffy");
            Assert.IsNotNull(fluffyImage);
            Assert.AreEqual("fluffy-001.jpg", fluffyImage.Filename);
            Assert.AreEqual("cats/fluffy/fluffy-001.jpg", fluffyImage.StorageKey);
            Assert.AreEqual("S3", fluffyImage.StorageProvider);
            Assert.AreEqual("gallery", fluffyImage.Category);
            Assert.AreEqual("fluffy,maine-coon,male", fluffyImage.Tags);
        }

        [TestMethod]
        public async Task CompleteS3ToDbMigrationWithB2Sync_ShouldMigrateAndSync()
        {
            // Arrange
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: true,
                dryRun: false);

            // Act
            await WaitForMigrationCompletion(migrationId, TimeSpan.FromMinutes(2));

            // Assert
            var status = await _migrationService.GetMigrationStatusAsync(migrationId);
            Assert.IsNotNull(status);
            Assert.AreEqual("Completed", status.Status);
            Assert.AreEqual(3, status.ProcessedItems);
            Assert.AreEqual(3, status.SuccessfulItems);
            Assert.AreEqual(0, status.FailedItems);

            // Verify data was migrated to database
            var allImages = await _galleryRepository.GetAllAsync(1, 100, activeOnly: false, publicOnly: false);
            Assert.AreEqual(3, allImages.TotalCount);

            // Verify B2 sync was performed
            var syncedImages = allImages.Items.Where(i => !string.IsNullOrEmpty(i.B2Key)).ToList();
            Assert.AreEqual(3, syncedImages.Count);

            // Verify B2 sync logs were created
            var syncLogs = await _context.B2SyncLogs.ToListAsync();
            Assert.AreEqual(3, syncLogs.Count);
            Assert.IsTrue(syncLogs.All(l => l.Status == "Completed"));
        }

        [TestMethod]
        public async Task MigrationWithValidation_ShouldValidateDataIntegrity()
        {
            // Arrange
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: false,
                dryRun: false);

            // Act
            await WaitForMigrationCompletion(migrationId, TimeSpan.FromMinutes(2));

            // Perform validation
            var validationResult = await _migrationValidator.ValidateDataIntegrityAsync("S3", 100);

            // Assert
            Assert.IsNotNull(validationResult);
            Assert.IsTrue(validationResult.IsValid);
            Assert.AreEqual(0, validationResult.ErrorCount);
            Assert.AreEqual(3, validationResult.ValidationResults.Count);
            Assert.IsTrue(validationResult.ValidationResults.All(r => r.Status == "Success"));
        }

        [TestMethod]
        public async Task MigrationWithReporting_ShouldGenerateReports()
        {
            // Arrange
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: true,
                dryRun: false);

            // Act
            await WaitForMigrationCompletion(migrationId, TimeSpan.FromMinutes(2));

            // Generate reports
            var dashboardReport = await _migrationReporter.GenerateDashboardReportAsync();
            var executiveSummary = await _migrationReporter.GenerateExecutiveSummaryAsync();

            // Assert
            Assert.IsNotNull(dashboardReport);
            Assert.IsNotNull(executiveSummary);
            
            // Verify dashboard report
            Assert.IsTrue(dashboardReport.TotalMigrations > 0);
            Assert.IsTrue(dashboardReport.TotalItemsProcessed > 0);
            Assert.IsTrue(dashboardReport.SuccessRate > 0.9); // 90%+ success rate
            
            // Verify executive summary
            Assert.IsTrue(executiveSummary.TotalMigrations > 0);
            Assert.IsTrue(executiveSummary.TotalImagesProcessed > 0);
            Assert.IsTrue(executiveSummary.OverallSuccessRate > 0.9);
        }

        [TestMethod]
        public async Task DryRunMigration_ShouldNotModifyDatabase()
        {
            // Arrange
            var initialImageCount = await _galleryRepository.GetTotalCountAsync(activeOnly: false, publicOnly: false);
            var initialSyncLogCount = await _context.B2SyncLogs.CountAsync();

            // Act
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: true,
                dryRun: true);

            await WaitForMigrationCompletion(migrationId, TimeSpan.FromMinutes(2));

            // Assert
            var status = await _migrationService.GetMigrationStatusAsync(migrationId);
            Assert.IsNotNull(status);
            Assert.AreEqual("Completed", status.Status);
            Assert.AreEqual(3, status.ProcessedItems); // Items were processed
            Assert.AreEqual(3, status.SuccessfulItems); // But not actually migrated

            // Verify no data was actually migrated
            var finalImageCount = await _galleryRepository.GetTotalCountAsync(activeOnly: false, publicOnly: false);
            var finalSyncLogCount = await _context.B2SyncLogs.CountAsync();
            
            Assert.AreEqual(initialImageCount, finalImageCount);
            Assert.AreEqual(initialSyncLogCount, finalSyncLogCount);
        }

        [TestMethod]
        public async Task MigrationWithErrors_ShouldHandleGracefully()
        {
            // Arrange - Setup S3 to fail for one object
            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync("cats/mittens/mittens-001.jpg"))
                .ThrowsAsync(new Exception("S3 access denied"));

            // Act
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: false,
                dryRun: false);

            await WaitForMigrationCompletion(migrationId, TimeSpan.FromMinutes(2));

            // Assert
            var status = await _migrationService.GetMigrationStatusAsync(migrationId);
            Assert.IsNotNull(status);
            Assert.AreEqual("Completed", status.Status);
            Assert.AreEqual(3, status.ProcessedItems);
            Assert.AreEqual(2, status.SuccessfulItems); // Only 2 should succeed
            Assert.AreEqual(1, status.FailedItems); // 1 should fail

            // Verify only successful items were migrated
            var allImages = await _galleryRepository.GetAllAsync(1, 100, activeOnly: false, publicOnly: false);
            Assert.AreEqual(2, allImages.TotalCount);
            
            // Verify error item is not in database
            var mittensImage = allImages.Items.FirstOrDefault(i => i.CatName == "Mittens");
            Assert.IsNull(mittensImage);
        }

        [TestMethod]
        public async Task MigrationCancellation_ShouldStopGracefully()
        {
            // Arrange
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 1, // Small batch size to allow cancellation
                enableB2Sync: false,
                dryRun: false);

            // Act - Cancel migration immediately
            await _migrationService.CancelMigrationAsync(migrationId);

            // Wait a bit for cancellation to take effect
            await Task.Delay(1000);

            // Assert
            var status = await _migrationService.GetMigrationStatusAsync(migrationId);
            Assert.IsNotNull(status);
            Assert.AreEqual("Cancelled", status.Status);
            
            // Verify partial migration state
            var processedCount = await _galleryRepository.GetTotalCountAsync(activeOnly: false, publicOnly: false);
            Assert.IsTrue(processedCount <= 3); // Should not have processed all items
        }

        [TestMethod]
        public async Task MigrationRetry_ShouldRetryFailedItems()
        {
            // Arrange - First migration with errors
            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync("cats/whiskers/whiskers-001.jpg"))
                .ThrowsAsync(new Exception("Temporary S3 error"));

            var firstMigrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: false,
                dryRun: false);

            await WaitForMigrationCompletion(firstMigrationId, TimeSpan.FromMinutes(2));

            // Act - Fix the error and retry
            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync("cats/whiskers/whiskers-001.jpg"))
                .ReturnsAsync(new S3ObjectMetadata
                {
                    ContentType = "image/jpeg",
                    ContentLength = 1536576,
                    LastModified = DateTime.UtcNow.AddDays(-7),
                    ETag = "etag3",
                    UserMetadata = new Dictionary<string, string>
                    {
                        ["cat-metadata"] = System.Text.Json.JsonSerializer.Serialize(new CatImageMetadata
                        {
                            Name = "Whiskers",
                            Gender = "M",
                            FileSize = 1536576,
                            ContentType = "image/jpeg",
                            Width = 1800,
                            Height = 1350,
                            Description = "Champion stud",
                            Breed = "Maine Coon",
                            Category = "studs",
                            Tags = "whiskers,maine-coon,male,champion",
                            DateUploaded = DateTime.UtcNow.AddDays(-7),
                            DateTaken = DateTime.UtcNow.AddDays(-7),
                            CatId = 3,
                            RegisteredName = "GCH Test Cattery's Whiskers"
                        })
                    }
                });

            var retryMigrationId = await _migrationService.StartMigrationAsync(
                batchSize: 10,
                enableB2Sync: false,
                dryRun: false);

            await WaitForMigrationCompletion(retryMigrationId, TimeSpan.FromMinutes(2));

            // Assert
            var retryStatus = await _migrationService.GetMigrationStatusAsync(retryMigrationId);
            Assert.IsNotNull(retryStatus);
            Assert.AreEqual("Completed", retryStatus.Status);
            Assert.AreEqual(3, retryStatus.SuccessfulItems);
            Assert.AreEqual(0, retryStatus.FailedItems);

            // Verify all items are now in database
            var allImages = await _galleryRepository.GetAllAsync(1, 100, activeOnly: false, publicOnly: false);
            Assert.AreEqual(3, allImages.TotalCount);
        }

        [TestMethod]
        public async Task MigrationPerformance_ShouldMeetTargets()
        {
            // Arrange - Create larger dataset
            var largeS3Objects = new List<S3ObjectInfo>();
            for (int i = 1; i <= 100; i++)
            {
                largeS3Objects.Add(new S3ObjectInfo
                {
                    Key = $"cats/perf-test/cat-{i:D3}.jpg",
                    Size = 1024576,
                    LastModified = DateTime.UtcNow.AddDays(-i),
                    ETag = $"etag{i}"
                });
            }

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(largeS3Objects);

            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync(It.IsAny<string>()))
                .ReturnsAsync(new S3ObjectMetadata
                {
                    ContentType = "image/jpeg",
                    ContentLength = 1024576,
                    LastModified = DateTime.UtcNow,
                    ETag = "etag",
                    UserMetadata = new Dictionary<string, string>
                    {
                        ["cat-metadata"] = System.Text.Json.JsonSerializer.Serialize(new CatImageMetadata
                        {
                            Name = "Performance Test Cat",
                            Gender = "M",
                            FileSize = 1024576,
                            ContentType = "image/jpeg",
                            Width = 1920,
                            Height = 1080,
                            Description = "Performance test image",
                            Breed = "Maine Coon",
                            Category = "gallery",
                            Tags = "performance,test",
                            DateUploaded = DateTime.UtcNow,
                            DateTaken = DateTime.UtcNow,
                            CatId = 1,
                            RegisteredName = "Performance Test Cat"
                        })
                    }
                });

            // Act
            var startTime = DateTime.UtcNow;
            
            var migrationId = await _migrationService.StartMigrationAsync(
                batchSize: 20,
                enableB2Sync: false,
                dryRun: false);

            await WaitForMigrationCompletion(migrationId, TimeSpan.FromMinutes(5));
            
            var endTime = DateTime.UtcNow;
            var totalTime = endTime - startTime;

            // Assert
            var status = await _migrationService.GetMigrationStatusAsync(migrationId);
            Assert.IsNotNull(status);
            Assert.AreEqual("Completed", status.Status);
            Assert.AreEqual(100, status.SuccessfulItems);
            Assert.AreEqual(0, status.FailedItems);

            // Performance targets
            var itemsPerSecond = 100.0 / totalTime.TotalSeconds;
            Assert.IsTrue(itemsPerSecond > 5, $"Migration speed: {itemsPerSecond:F2} items/sec (target: >5)");
            Assert.IsTrue(totalTime.TotalMinutes < 2, $"Total time: {totalTime.TotalMinutes:F1} minutes (target: <2)");
        }

        private async Task WaitForMigrationCompletion(string migrationId, TimeSpan timeout)
        {
            var endTime = DateTime.UtcNow.Add(timeout);
            
            while (DateTime.UtcNow < endTime)
            {
                var status = await _migrationService.GetMigrationStatusAsync(migrationId);
                if (status != null && (status.Status == "Completed" || status.Status == "Failed" || status.Status == "Cancelled"))
                {
                    return;
                }
                
                await Task.Delay(100); // Check every 100ms
            }
            
            throw new TimeoutException($"Migration {migrationId} did not complete within {timeout}");
        }
    }

    // Helper classes for testing
    public class B2UploadResult
    {
        public string FileId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string AccountId { get; set; } = string.Empty;
        public string BucketId { get; set; } = string.Empty;
        public long ContentLength { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public DateTime UploadTimestamp { get; set; }
    }

    public interface IB2StorageService
    {
        Task<B2UploadResult> UploadFileAsync(string fileName, byte[] content, string contentType);
    }
}
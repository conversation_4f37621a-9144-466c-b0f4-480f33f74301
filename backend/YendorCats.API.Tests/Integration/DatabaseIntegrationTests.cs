using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using YendorCats.API.Data;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Integration
{
    [TestClass]
    public class DatabaseIntegrationTests
    {
        private ServiceProvider _serviceProvider;
        private AppDbContext _context;
        private IGalleryRepository _galleryRepository;
        private ICatProfileRepository _catProfileRepository;
        private SqliteConnection _connection;

        [TestInitialize]
        public async Task Setup()
        {
            // Create in-memory SQLite database
            _connection = new SqliteConnection("DataSource=:memory:");
            _connection.Open();

            // Configure services
            var services = new ServiceCollection();
            services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite(_connection));
            services.AddScoped<IGalleryRepository, GalleryRepository>();
            services.AddScoped<ICatProfileRepository, CatProfileRepository>();
            services.AddLogging();

            _serviceProvider = services.BuildServiceProvider();
            _context = _serviceProvider.GetRequiredService<AppDbContext>();
            _galleryRepository = _serviceProvider.GetRequiredService<IGalleryRepository>();
            _catProfileRepository = _serviceProvider.GetRequiredService<ICatProfileRepository>();

            // Create database schema
            await _context.Database.EnsureCreatedAsync();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _serviceProvider?.Dispose();
            _connection?.Close();
            _connection?.Dispose();
        }

        [TestMethod]
        public async Task DatabaseSchema_ShouldBeCreatedSuccessfully()
        {
            // Arrange & Act
            var canConnect = await _context.Database.CanConnectAsync();

            // Assert
            Assert.IsTrue(canConnect);
            
            // Verify tables exist
            var tableNames = await _context.Database.SqlQueryRaw<string>(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                .ToListAsync();

            Assert.IsTrue(tableNames.Contains("CatGalleryImages"));
            Assert.IsTrue(tableNames.Contains("CatProfiles"));
            Assert.IsTrue(tableNames.Contains("B2SyncLogs"));
        }

        [TestMethod]
        public async Task CatGalleryImages_IndexesAndConstraints_ShouldWork()
        {
            // Arrange
            var image1 = new CatGalleryImage
            {
                Filename = "test1.jpg",
                StorageKey = "cats/test1.jpg",
                OriginalFileName = "test1.jpg",
                Category = "gallery",
                CatId = "cat1",
                CatName = "Test Cat 1",
                StorageProvider = "S3",
                StorageBucketName = "test-bucket",
                IsActive = true,
                IsPublic = true,
                DateTaken = DateTime.UtcNow.AddDays(-30),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var image2 = new CatGalleryImage
            {
                Filename = "test2.jpg",
                StorageKey = "cats/test2.jpg", // Same storage key should fail unique constraint
                OriginalFileName = "test2.jpg",
                Category = "gallery",
                CatId = "cat2",
                CatName = "Test Cat 2",
                StorageProvider = "S3",
                StorageBucketName = "test-bucket",
                IsActive = true,
                IsPublic = true,
                DateTaken = DateTime.UtcNow.AddDays(-15),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Act & Assert
            await _galleryRepository.CreateAsync(image1);
            
            // Duplicate storage key should fail
            await Assert.ThrowsExceptionAsync<InvalidOperationException>(async () =>
            {
                image2.StorageKey = "cats/test1.jpg"; // Duplicate key
                await _galleryRepository.CreateAsync(image2);
            });

            // Unique storage key should succeed
            image2.StorageKey = "cats/test2.jpg";
            var result = await _galleryRepository.CreateAsync(image2);
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public async Task CatGalleryImages_PerformanceIndexes_ShouldOptimizeQueries()
        {
            // Arrange - Create test data
            var images = new List<CatGalleryImage>();
            for (int i = 1; i <= 100; i++)
            {
                images.Add(new CatGalleryImage
                {
                    Filename = $"test{i}.jpg",
                    StorageKey = $"cats/test{i}.jpg",
                    OriginalFileName = $"test{i}.jpg",
                    Category = i % 3 == 0 ? "gallery" : i % 3 == 1 ? "studs" : "queens",
                    CatId = $"cat{i % 10}",
                    CatName = $"Test Cat {i % 10}",
                    StorageProvider = "S3",
                    StorageBucketName = "test-bucket",
                    IsActive = i % 4 != 0, // 75% active
                    IsPublic = i % 5 != 0, // 80% public
                    DateTaken = DateTime.UtcNow.AddDays(-i),
                    CreatedAt = DateTime.UtcNow.AddDays(-i),
                    UpdatedAt = DateTime.UtcNow.AddDays(-i)
                });
            }

            // Bulk insert
            foreach (var image in images)
            {
                await _galleryRepository.CreateAsync(image);
            }

            // Act & Assert - Test indexed queries
            var start = DateTime.UtcNow;
            
            // Test category index
            var galleryImages = await _galleryRepository.GetByCategoryAsync("gallery", 1, 50);
            var categoryQueryTime = DateTime.UtcNow - start;
            
            start = DateTime.UtcNow;
            
            // Test cat ID index
            var catImages = await _galleryRepository.GetByCatIdAsync("cat1", 1, 50);
            var catIdQueryTime = DateTime.UtcNow - start;
            
            start = DateTime.UtcNow;
            
            // Test active/public index
            var activeImages = await _galleryRepository.GetAllAsync(1, 50);
            var activeQueryTime = DateTime.UtcNow - start;

            // Assert results and performance
            Assert.IsTrue(galleryImages.TotalCount > 0);
            Assert.IsTrue(catImages.TotalCount > 0);
            Assert.IsTrue(activeImages.TotalCount > 0);
            
            // Performance should be reasonable (under 100ms for 100 records)
            Assert.IsTrue(categoryQueryTime.TotalMilliseconds < 100);
            Assert.IsTrue(catIdQueryTime.TotalMilliseconds < 100);
            Assert.IsTrue(activeQueryTime.TotalMilliseconds < 100);
        }

        [TestMethod]
        public async Task CatProfiles_RelationshipWithImages_ShouldWork()
        {
            // Arrange
            var profile = new CatProfile
            {
                Name = "Test Cat",
                CatId = "test-cat-1",
                Breed = "Maine Coon",
                Gender = "M",
                DateOfBirth = new DateTime(2020, 6, 15),
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var image = new CatGalleryImage
            {
                Filename = "test-cat-1.jpg",
                StorageKey = "cats/test-cat-1.jpg",
                OriginalFileName = "test-cat-1.jpg",
                Category = "gallery",
                CatId = "test-cat-1",
                CatName = "Test Cat",
                StorageProvider = "S3",
                StorageBucketName = "test-bucket",
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Act
            var createdProfile = await _catProfileRepository.CreateAsync(profile);
            var createdImage = await _galleryRepository.CreateAsync(image);

            // Assert
            Assert.IsNotNull(createdProfile);
            Assert.IsNotNull(createdImage);
            
            // Test relationship query
            var profileImages = await _galleryRepository.GetByCatIdAsync("test-cat-1", 1, 10);
            Assert.AreEqual(1, profileImages.TotalCount);
            Assert.AreEqual("test-cat-1.jpg", profileImages.Items[0].Filename);
        }

        [TestMethod]
        public async Task B2SyncLogs_ConcurrentOperations_ShouldHandleCorrectly()
        {
            // Arrange
            var syncLogs = new List<B2SyncLog>();
            for (int i = 1; i <= 10; i++)
            {
                syncLogs.Add(new B2SyncLog
                {
                    Operation = "Upload",
                    SourceStorageProvider = "S3",
                    SourceKey = $"cats/test{i}.jpg",
                    SourceBucket = "source-bucket",
                    DestinationStorageProvider = "B2",
                    DestinationKey = $"cats/test{i}.jpg",
                    DestinationBucket = "destination-bucket",
                    FileSize = 1024576,
                    ContentType = "image/jpeg",
                    Status = "Pending",
                    StartTime = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
            }

            // Act - Simulate concurrent operations
            var tasks = syncLogs.Select(async log =>
            {
                _context.B2SyncLogs.Add(log);
                await _context.SaveChangesAsync();
                return log;
            });

            var results = await Task.WhenAll(tasks);

            // Assert
            Assert.AreEqual(10, results.Length);
            Assert.IsTrue(results.All(r => r.Id > 0));
            
            // Verify all logs were saved
            var savedLogs = await _context.B2SyncLogs.ToListAsync();
            Assert.AreEqual(10, savedLogs.Count);
        }

        [TestMethod]
        public async Task Database_TransactionRollback_ShouldWorkCorrectly()
        {
            // Arrange
            var profile = new CatProfile
            {
                Name = "Test Cat",
                CatId = "test-cat-1",
                Breed = "Maine Coon",
                Gender = "M",
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var image = new CatGalleryImage
            {
                Filename = "test-cat-1.jpg",
                StorageKey = "cats/test-cat-1.jpg",
                OriginalFileName = "test-cat-1.jpg",
                Category = "gallery",
                CatId = "test-cat-1",
                CatName = "Test Cat",
                StorageProvider = "S3",
                StorageBucketName = "test-bucket",
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Act & Assert
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Add profile
                _context.CatProfiles.Add(profile);
                await _context.SaveChangesAsync();
                
                // Add image
                _context.CatGalleryImages.Add(image);
                await _context.SaveChangesAsync();
                
                // Verify both exist in transaction
                var profileCount = await _context.CatProfiles.CountAsync();
                var imageCount = await _context.CatGalleryImages.CountAsync();
                Assert.AreEqual(1, profileCount);
                Assert.AreEqual(1, imageCount);
                
                // Rollback transaction
                await transaction.RollbackAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
            
            // Verify rollback worked
            var finalProfileCount = await _context.CatProfiles.CountAsync();
            var finalImageCount = await _context.CatGalleryImages.CountAsync();
            Assert.AreEqual(0, finalProfileCount);
            Assert.AreEqual(0, finalImageCount);
        }

        [TestMethod]
        public async Task Database_ConcurrentReadWrite_ShouldHandleCorrectly()
        {
            // Arrange
            var baseImage = new CatGalleryImage
            {
                Filename = "concurrent-test.jpg",
                StorageKey = "cats/concurrent-test.jpg",
                OriginalFileName = "concurrent-test.jpg",
                Category = "gallery",
                CatId = "concurrent-cat",
                CatName = "Concurrent Cat",
                StorageProvider = "S3",
                StorageBucketName = "test-bucket",
                IsActive = true,
                IsPublic = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var createdImage = await _galleryRepository.CreateAsync(baseImage);

            // Act - Simulate concurrent view count updates
            var tasks = new List<Task>();
            for (int i = 0; i < 10; i++)
            {
                tasks.Add(Task.Run(async () =>
                {
                    var image = await _galleryRepository.GetByIdAsync(createdImage.Id);
                    if (image != null)
                    {
                        image.ViewCount++;
                        await _galleryRepository.UpdateAsync(image);
                    }
                }));
            }

            await Task.WhenAll(tasks);

            // Assert
            var finalImage = await _galleryRepository.GetByIdAsync(createdImage.Id);
            Assert.IsNotNull(finalImage);
            
            // Due to concurrency, the final view count may not be exactly 10
            // but should be greater than 0 and less than or equal to 10
            Assert.IsTrue(finalImage.ViewCount > 0);
            Assert.IsTrue(finalImage.ViewCount <= 10);
        }

        [TestMethod]
        public async Task Database_BulkOperations_ShouldPerformWell()
        {
            // Arrange
            var images = new List<CatGalleryImage>();
            for (int i = 1; i <= 1000; i++)
            {
                images.Add(new CatGalleryImage
                {
                    Filename = $"bulk{i}.jpg",
                    StorageKey = $"cats/bulk{i}.jpg",
                    OriginalFileName = $"bulk{i}.jpg",
                    Category = "gallery",
                    CatId = $"bulk-cat-{i % 100}",
                    CatName = $"Bulk Cat {i % 100}",
                    StorageProvider = "S3",
                    StorageBucketName = "test-bucket",
                    IsActive = true,
                    IsPublic = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
            }

            // Act
            var start = DateTime.UtcNow;
            
            // Bulk insert using Entity Framework
            _context.CatGalleryImages.AddRange(images);
            await _context.SaveChangesAsync();
            
            var insertTime = DateTime.UtcNow - start;
            
            start = DateTime.UtcNow;
            
            // Bulk read
            var allImages = await _context.CatGalleryImages.ToListAsync();
            
            var readTime = DateTime.UtcNow - start;
            
            // Assert
            Assert.AreEqual(1000, allImages.Count);
            
            // Performance should be reasonable
            Assert.IsTrue(insertTime.TotalSeconds < 10, $"Bulk insert took {insertTime.TotalSeconds} seconds");
            Assert.IsTrue(readTime.TotalSeconds < 2, $"Bulk read took {readTime.TotalSeconds} seconds");
        }

        [TestMethod]
        public async Task Database_ComplexQueries_ShouldWorkCorrectly()
        {
            // Arrange - Create test data with relationships
            var profiles = new List<CatProfile>();
            var images = new List<CatGalleryImage>();
            
            for (int i = 1; i <= 50; i++)
            {
                var profile = new CatProfile
                {
                    Name = $"Cat {i}",
                    CatId = $"cat-{i}",
                    Breed = i % 3 == 0 ? "Maine Coon" : i % 3 == 1 ? "Persian" : "Siamese",
                    Gender = i % 2 == 0 ? "M" : "F",
                    DateOfBirth = DateTime.UtcNow.AddYears(-i % 10 - 1),
                    IsActive = i % 4 != 0,
                    IsPublic = i % 5 != 0,
                    IsBreeding = i % 3 == 0,
                    ViewCount = i * 10,
                    LikeCount = i * 5,
                    OffspringCount = i % 4 == 0 ? i / 4 : 0,
                    CreatedAt = DateTime.UtcNow.AddDays(-i),
                    UpdatedAt = DateTime.UtcNow.AddDays(-i)
                };
                profiles.Add(profile);
                
                // Add 2-3 images per profile
                for (int j = 1; j <= (i % 3) + 1; j++)
                {
                    images.Add(new CatGalleryImage
                    {
                        Filename = $"cat-{i}-{j}.jpg",
                        StorageKey = $"cats/cat-{i}-{j}.jpg",
                        OriginalFileName = $"cat-{i}-{j}.jpg",
                        Category = j == 1 ? "gallery" : j == 2 ? "studs" : "queens",
                        CatId = $"cat-{i}",
                        CatName = $"Cat {i}",
                        StorageProvider = "S3",
                        StorageBucketName = "test-bucket",
                        IsActive = i % 4 != 0,
                        IsPublic = i % 5 != 0,
                        IsFeatured = i % 10 == 0,
                        ViewCount = i * j * 10,
                        LikeCount = i * j * 5,
                        Tags = $"tag{i % 5},tag{j}",
                        CreatedAt = DateTime.UtcNow.AddDays(-i),
                        UpdatedAt = DateTime.UtcNow.AddDays(-i)
                    });
                }
            }

            // Insert data
            _context.CatProfiles.AddRange(profiles);
            _context.CatGalleryImages.AddRange(images);
            await _context.SaveChangesAsync();

            // Act & Assert - Test complex queries
            
            // 1. Get breeding cats with their images
            var breedingCats = await _catProfileRepository.GetBreedingCatsAsync(1, 20);
            Assert.IsTrue(breedingCats.TotalCount > 0);
            Assert.IsTrue(breedingCats.Items.All(c => c.IsBreeding));
            
            // 2. Get most popular images
            var popularImages = await _galleryRepository.GetPopularAsync(1, 10);
            Assert.IsTrue(popularImages.TotalCount > 0);
            Assert.IsTrue(popularImages.Items[0].ViewCount >= popularImages.Items[1].ViewCount);
            
            // 3. Get featured images
            var featuredImages = await _galleryRepository.GetFeaturedAsync(1, 10);
            Assert.IsTrue(featuredImages.TotalCount > 0);
            Assert.IsTrue(featuredImages.Items.All(i => i.IsFeatured));
            
            // 4. Search by tags
            var taggedImages = await _galleryRepository.GetByTagsAsync(new[] { "tag1", "tag2" }, 1, 20);
            Assert.IsTrue(taggedImages.TotalCount > 0);
            
            // 5. Get cats by age range
            var youngCats = await _catProfileRepository.GetByAgeRangeAsync(1, 3, 1, 20);
            Assert.IsTrue(youngCats.TotalCount > 0);
            Assert.IsTrue(youngCats.Items.All(c => c.Age >= 1 && c.Age <= 3));
            
            // 6. Get storage provider stats
            var storageStats = await _galleryRepository.GetStorageProviderStatsAsync();
            Assert.IsTrue(storageStats.Any());
            Assert.IsTrue(storageStats.All(s => s.Count > 0));
        }

        [TestMethod]
        public async Task Database_DataIntegrity_ShouldBeEnforced()
        {
            // Arrange
            var profile = new CatProfile
            {
                Name = "Integrity Test Cat",
                CatId = "integrity-cat",
                Breed = "Maine Coon",
                Gender = "M",
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var image = new CatGalleryImage
            {
                Filename = "integrity-test.jpg",
                StorageKey = "cats/integrity-test.jpg",
                OriginalFileName = "integrity-test.jpg",
                Category = "gallery",
                CatId = "integrity-cat",
                CatName = "Integrity Test Cat",
                StorageProvider = "S3",
                StorageBucketName = "test-bucket",
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Act & Assert
            
            // 1. Test required field validation
            var invalidProfile = new CatProfile(); // Missing required fields
            await Assert.ThrowsExceptionAsync<DbUpdateException>(async () =>
            {
                _context.CatProfiles.Add(invalidProfile);
                await _context.SaveChangesAsync();
            });
            
            // 2. Test string length constraints
            var longNameProfile = new CatProfile
            {
                Name = new string('a', 101), // Exceeds max length
                CatId = "long-name-cat",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            
            await Assert.ThrowsExceptionAsync<DbUpdateException>(async () =>
            {
                _context.CatProfiles.Add(longNameProfile);
                await _context.SaveChangesAsync();
            });
            
            // 3. Test successful save with valid data
            var validProfile = await _catProfileRepository.CreateAsync(profile);
            var validImage = await _galleryRepository.CreateAsync(image);
            
            Assert.IsNotNull(validProfile);
            Assert.IsNotNull(validImage);
            Assert.IsTrue(validProfile.Id > 0);
            Assert.IsTrue(validImage.Id > 0);
        }
    }
}
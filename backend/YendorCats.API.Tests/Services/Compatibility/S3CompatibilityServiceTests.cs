using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Services;
using YendorCats.API.Services.Compatibility;
using YendorCats.API.Services.Migration;

namespace YendorCats.API.Tests.Services.Compatibility
{
    [TestClass]
    public class S3CompatibilityServiceTests
    {
        private Mock<IGalleryRepository> _mockGalleryRepository;
        private Mock<IS3StorageService> _mockS3StorageService;
        private Mock<IS3ToDbMigrationService> _mockMigrationService;
        private Mock<ILogger<S3CompatibilityService>> _mockLogger;
        private S3CompatibilityService _service;
        private List<CatGalleryImage> _testImages;
        private List<S3ObjectInfo> _testS3Objects;

        [TestInitialize]
        public void Setup()
        {
            _mockGalleryRepository = new Mock<IGalleryRepository>();
            _mockS3StorageService = new Mock<IS3StorageService>();
            _mockMigrationService = new Mock<IS3ToDbMigrationService>();
            _mockLogger = new Mock<ILogger<S3CompatibilityService>>();
            
            _service = new S3CompatibilityService(
                _mockGalleryRepository.Object,
                _mockS3StorageService.Object,
                _mockMigrationService.Object,
                _mockLogger.Object);

            _testImages = new List<CatGalleryImage>
            {
                new CatGalleryImage
                {
                    Id = 1,
                    Filename = "test1.jpg",
                    StorageKey = "cats/test1.jpg",
                    CatId = "cat1",
                    CatName = "Fluffy",
                    Description = "A fluffy cat",
                    FileSize = 1024576,
                    Width = 1920,
                    Height = 1080,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddDays(-30),
                    Tags = "fluffy,cute",
                    IsActive = true,
                    IsFeatured = true,
                    ViewCount = 100,
                    LikeCount = 50,
                    S3Url = "https://test-bucket.s3.amazonaws.com/cats/test1.jpg",
                    StorageProvider = "S3"
                },
                new CatGalleryImage
                {
                    Id = 2,
                    Filename = "test2.jpg",
                    StorageKey = "cats/test2.jpg",
                    CatId = "cat2",
                    CatName = "Mittens",
                    Description = "A playful cat",
                    FileSize = 2048576,
                    Width = 1600,
                    Height = 1200,
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    UpdatedAt = DateTime.UtcNow.AddDays(-15),
                    Tags = "mittens,playful",
                    IsActive = true,
                    IsFeatured = false,
                    ViewCount = 75,
                    LikeCount = 25,
                    S3Url = "https://test-bucket.s3.amazonaws.com/cats/test2.jpg",
                    StorageProvider = "S3"
                }
            };

            _testS3Objects = new List<S3ObjectInfo>
            {
                new S3ObjectInfo
                {
                    Key = "cats/test1.jpg",
                    Size = 1024576,
                    LastModified = DateTime.UtcNow.AddDays(-30),
                    ETag = "etag1"
                },
                new S3ObjectInfo
                {
                    Key = "cats/test2.jpg",
                    Size = 2048576,
                    LastModified = DateTime.UtcNow.AddDays(-15),
                    ETag = "etag2"
                }
            };
        }

        [TestMethod]
        public async Task GetAllImagesAsync_WithDatabaseImages_ShouldReturnFromDatabase()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetAllAsync(1, int.MaxValue))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = _testImages,
                    TotalCount = 2
                });

            // Act
            var result = await _service.GetAllImagesAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count);
            Assert.AreEqual("test1.jpg", result[0].Filename);
            Assert.AreEqual("Fluffy", result[0].CatName);
            
            // Verify database was called but S3 was not
            _mockGalleryRepository.Verify(x => x.GetAllAsync(1, int.MaxValue), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Never);
        }

        [TestMethod]
        public async Task GetAllImagesAsync_WithoutDatabaseImages_ShouldFallbackToS3()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetAllAsync(1, int.MaxValue))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = new List<CatGalleryImage>(),
                    TotalCount = 0
                });

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync(It.IsAny<string>()))
                .ReturnsAsync(new S3ObjectMetadata
                {
                    ContentType = "image/jpeg",
                    ContentLength = 1024576,
                    LastModified = DateTime.UtcNow.AddDays(-30),
                    ETag = "etag1"
                });

            // Act
            var result = await _service.GetAllImagesAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count);
            Assert.AreEqual("test1.jpg", result[0].Filename);
            Assert.AreEqual("Test1", result[0].CatName); // Extracted from filename
            
            // Verify both database and S3 were called
            _mockGalleryRepository.Verify(x => x.GetAllAsync(1, int.MaxValue), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Once);
        }

        [TestMethod]
        public async Task GetImagesByCategoryAsync_WithDatabaseImages_ShouldReturnFromDatabase()
        {
            // Arrange
            var categoryImages = _testImages.Where(i => i.CatId == "cat1").ToList();
            _mockGalleryRepository.Setup(x => x.GetByCatIdAsync("cat1", 1, int.MaxValue))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = categoryImages,
                    TotalCount = 1
                });

            // Act
            var result = await _service.GetImagesByCategoryAsync("cat1");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Count);
            Assert.AreEqual("cat1", result[0].Category);
            
            // Verify database was called but S3 was not
            _mockGalleryRepository.Verify(x => x.GetByCatIdAsync("cat1", 1, int.MaxValue), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Never);
        }

        [TestMethod]
        public async Task GetImagesByCategoryAsync_WithoutDatabaseImages_ShouldFallbackToS3()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetByCatIdAsync("cat1", 1, int.MaxValue))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = new List<CatGalleryImage>(),
                    TotalCount = 0
                });

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync(It.IsAny<string>()))
                .ReturnsAsync(new S3ObjectMetadata
                {
                    ContentType = "image/jpeg",
                    ContentLength = 1024576,
                    LastModified = DateTime.UtcNow.AddDays(-30),
                    ETag = "etag1"
                });

            // Act
            var result = await _service.GetImagesByCategoryAsync("cat1");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count); // Both objects from S3 (filtering by category happens in S3)
            
            // Verify both database and S3 were called
            _mockGalleryRepository.Verify(x => x.GetByCatIdAsync("cat1", 1, int.MaxValue), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Once);
        }

        [TestMethod]
        public async Task GetImageByFilenameAsync_WithDatabaseImage_ShouldReturnFromDatabase()
        {
            // Arrange
            var testImage = _testImages[0];
            _mockGalleryRepository.Setup(x => x.GetByFilenameAsync("test1.jpg"))
                .ReturnsAsync(testImage);

            // Act
            var result = await _service.GetImageByFilenameAsync("test1.jpg");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("test1.jpg", result.Filename);
            Assert.AreEqual("Fluffy", result.CatName);
            
            // Verify database was called but S3 was not
            _mockGalleryRepository.Verify(x => x.GetByFilenameAsync("test1.jpg"), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Never);
        }

        [TestMethod]
        public async Task GetImageByFilenameAsync_WithoutDatabaseImage_ShouldFallbackToS3()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetByFilenameAsync("test1.jpg"))
                .ReturnsAsync((CatGalleryImage)null);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync("cats/test1.jpg"))
                .ReturnsAsync(new S3ObjectMetadata
                {
                    ContentType = "image/jpeg",
                    ContentLength = 1024576,
                    LastModified = DateTime.UtcNow.AddDays(-30),
                    ETag = "etag1"
                });

            // Act
            var result = await _service.GetImageByFilenameAsync("test1.jpg");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("test1.jpg", result.Filename);
            Assert.AreEqual("Test1", result.CatName); // Extracted from filename
            
            // Verify both database and S3 were called
            _mockGalleryRepository.Verify(x => x.GetByFilenameAsync("test1.jpg"), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Once);
        }

        [TestMethod]
        public async Task GetImageByFilenameAsync_WithNonexistentImage_ShouldReturnNull()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetByFilenameAsync("nonexistent.jpg"))
                .ReturnsAsync((CatGalleryImage)null);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            // Act
            var result = await _service.GetImageByFilenameAsync("nonexistent.jpg");

            // Assert
            Assert.IsNull(result);
            
            // Verify both database and S3 were called
            _mockGalleryRepository.Verify(x => x.GetByFilenameAsync("nonexistent.jpg"), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Once);
        }

        [TestMethod]
        public async Task GetImageMetadataAsync_WithDatabaseImage_ShouldReturnFromDatabase()
        {
            // Arrange
            var testImage = _testImages[0];
            _mockGalleryRepository.Setup(x => x.GetByFilenameAsync("test1.jpg"))
                .ReturnsAsync(testImage);

            // Act
            var result = await _service.GetImageMetadataAsync("test1.jpg");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("test1.jpg", result.Filename);
            Assert.AreEqual(1024576, result.FileSize);
            Assert.AreEqual(1920, result.Width);
            Assert.AreEqual(1080, result.Height);
            
            // Verify database was called but S3 was not
            _mockGalleryRepository.Verify(x => x.GetByFilenameAsync("test1.jpg"), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Never);
        }

        [TestMethod]
        public async Task GetImageMetadataAsync_WithoutDatabaseImage_ShouldFallbackToS3()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetByFilenameAsync("test1.jpg"))
                .ReturnsAsync((CatGalleryImage)null);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            _mockS3StorageService.Setup(x => x.GetObjectMetadataAsync("cats/test1.jpg"))
                .ReturnsAsync(new S3ObjectMetadata
                {
                    ContentType = "image/jpeg",
                    ContentLength = 1024576,
                    LastModified = DateTime.UtcNow.AddDays(-30),
                    ETag = "etag1"
                });

            // Act
            var result = await _service.GetImageMetadataAsync("test1.jpg");

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("test1.jpg", result.Filename);
            Assert.AreEqual(1024576, result.FileSize);
            Assert.AreEqual("image/jpeg", result.MimeType);
            
            // Verify both database and S3 were called
            _mockGalleryRepository.Verify(x => x.GetByFilenameAsync("test1.jpg"), Times.Once);
            _mockS3StorageService.Verify(x => x.ListObjectsAsync(), Times.Once);
        }

        [TestMethod]
        public async Task IsMigrationNeededAsync_WithEmptyDatabase_ShouldReturnTrue()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ReturnsAsync(0);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            // Act
            var result = await _service.IsMigrationNeededAsync();

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task IsMigrationNeededAsync_WithSyncedDatabase_ShouldReturnFalse()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ReturnsAsync(2);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            // Act
            var result = await _service.IsMigrationNeededAsync();

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task IsMigrationNeededAsync_WithPartialDatabase_ShouldReturnTrue()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ReturnsAsync(1);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            // Act
            var result = await _service.IsMigrationNeededAsync();

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task StartMigrationIfNeededAsync_WithMigrationNeeded_ShouldStartMigration()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ReturnsAsync(0);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            _mockMigrationService.Setup(x => x.StartMigrationAsync(50, true, false))
                .ReturnsAsync("migration-123");

            // Act
            var result = await _service.StartMigrationIfNeededAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("migration-123", result);
            
            // Verify migration was started
            _mockMigrationService.Verify(x => x.StartMigrationAsync(50, true, false), Times.Once);
        }

        [TestMethod]
        public async Task StartMigrationIfNeededAsync_WithoutMigrationNeeded_ShouldReturnNull()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ReturnsAsync(2);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            // Act
            var result = await _service.StartMigrationIfNeededAsync();

            // Assert
            Assert.IsNull(result);
            
            // Verify migration was not started
            _mockMigrationService.Verify(x => x.StartMigrationAsync(It.IsAny<int>(), It.IsAny<bool>(), It.IsAny<bool>()), Times.Never);
        }

        [TestMethod]
        public async Task GetMigrationProgressAsync_WithActiveMigration_ShouldReturnProgress()
        {
            // Arrange
            var migrationStatus = new MigrationStatus
            {
                Id = "migration-123",
                Status = "Running",
                ProgressPercentage = 50.0,
                ProcessedItems = 50,
                TotalItems = 100,
                SuccessfulItems = 48,
                FailedItems = 2,
                StartTime = DateTime.UtcNow.AddMinutes(-10),
                EndTime = null,
                EstimatedTimeRemaining = TimeSpan.FromMinutes(10)
            };

            _mockMigrationService.Setup(x => x.GetAllMigrationStatusesAsync())
                .ReturnsAsync(new List<MigrationStatus> { migrationStatus });

            // Act
            var result = await _service.GetMigrationProgressAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsRunning);
            Assert.AreEqual("Running", result.Status);
            Assert.AreEqual(50.0, result.ProgressPercentage);
            Assert.AreEqual(50, result.ProcessedItems);
            Assert.AreEqual(100, result.TotalItems);
        }

        [TestMethod]
        public async Task GetMigrationProgressAsync_WithoutActiveMigration_ShouldReturnRecentCompleted()
        {
            // Arrange
            var completedMigration = new MigrationStatus
            {
                Id = "migration-123",
                Status = "Completed",
                ProgressPercentage = 100.0,
                ProcessedItems = 100,
                TotalItems = 100,
                SuccessfulItems = 98,
                FailedItems = 2,
                StartTime = DateTime.UtcNow.AddMinutes(-30),
                EndTime = DateTime.UtcNow.AddMinutes(-20),
                EstimatedTimeRemaining = null
            };

            _mockMigrationService.Setup(x => x.GetAllMigrationStatusesAsync())
                .ReturnsAsync(new List<MigrationStatus> { completedMigration });

            // Act
            var result = await _service.GetMigrationProgressAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsRunning);
            Assert.AreEqual("Completed", result.Status);
            Assert.AreEqual(100.0, result.ProgressPercentage);
            Assert.IsNotNull(result.EndTime);
        }

        [TestMethod]
        public async Task GetMigrationProgressAsync_WithNoMigrations_ShouldReturnNull()
        {
            // Arrange
            _mockMigrationService.Setup(x => x.GetAllMigrationStatusesAsync())
                .ReturnsAsync(new List<MigrationStatus>());

            // Act
            var result = await _service.GetMigrationProgressAsync();

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetSystemStatusAsync_ShouldReturnSystemStatus()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ReturnsAsync(2);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            _mockMigrationService.Setup(x => x.GetAllMigrationStatusesAsync())
                .ReturnsAsync(new List<MigrationStatus>());

            // Act
            var result = await _service.GetSystemStatusAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.DatabaseConnected);
            Assert.IsTrue(result.S3Connected);
            Assert.IsFalse(result.MigrationActive);
            Assert.AreEqual(2, result.DatabaseImageCount);
            Assert.AreEqual(2, result.S3ImageCount);
            Assert.AreEqual("Synchronized", result.SyncStatus);
        }

        [TestMethod]
        public async Task GetSystemStatusAsync_WithMigrationActive_ShouldShowMigrationActive()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ReturnsAsync(1);

            _mockS3StorageService.Setup(x => x.ListObjectsAsync())
                .ReturnsAsync(_testS3Objects);

            var activeMigration = new MigrationStatus
            {
                Id = "migration-123",
                Status = "Running"
            };

            _mockMigrationService.Setup(x => x.GetAllMigrationStatusesAsync())
                .ReturnsAsync(new List<MigrationStatus> { activeMigration });

            // Act
            var result = await _service.GetSystemStatusAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.MigrationActive);
            Assert.AreEqual("Out of sync", result.SyncStatus);
        }

        [TestMethod]
        public async Task GetSystemStatusAsync_WithException_ShouldReturnErrorStatus()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetTotalCountAsync(false, false))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _service.GetSystemStatusAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.DatabaseConnected);
            Assert.IsFalse(result.S3Connected);
            Assert.AreEqual("Database error", result.ErrorMessage);
        }
    }
}
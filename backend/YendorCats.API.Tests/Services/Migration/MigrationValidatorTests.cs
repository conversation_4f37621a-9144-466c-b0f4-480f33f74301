using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Services.Migration;

namespace YendorCats.API.Tests.Services.Migration
{
    [TestClass]
    public class MigrationValidatorTests
    {
        private Mock<IGalleryRepository> _mockGalleryRepository;
        private Mock<ILogger<MigrationValidator>> _mockLogger;
        private MigrationValidator _validator;
        private List<CatGalleryImage> _testImages;

        [TestInitialize]
        public void Setup()
        {
            _mockGalleryRepository = new Mock<IGalleryRepository>();
            _mockLogger = new Mock<ILogger<MigrationValidator>>();
            _validator = new MigrationValidator(_mockGalleryRepository.Object, _mockLogger.Object);

            _testImages = new List<CatGalleryImage>
            {
                new CatGalleryImage
                {
                    Id = 1,
                    Filename = "test1.jpg",
                    StorageKey = "cats/test1.jpg",
                    FileSize = 1024576,
                    S3Key = "cats/test1.jpg",
                    S3Bucket = "test-bucket",
                    B2Key = "cats/test1.jpg",
                    B2Bucket = "test-bucket-b2",
                    StorageProvider = "B2",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                },
                new CatGalleryImage
                {
                    Id = 2,
                    Filename = "test2.jpg",
                    StorageKey = "cats/test2.jpg",
                    FileSize = 2048576,
                    S3Key = "cats/test2.jpg",
                    S3Bucket = "test-bucket",
                    B2Key = null, // Missing B2 data
                    B2Bucket = null,
                    StorageProvider = "S3",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                }
            };
        }

        [TestMethod]
        public async Task ValidateDataIntegrityAsync_WithValidData_ShouldPassValidation()
        {
            // Arrange
            var validImages = new List<CatGalleryImage> { _testImages[0] };
            _mockGalleryRepository.Setup(x => x.GetMigrationCandidatesAsync("B2", It.IsAny<int>()))
                .ReturnsAsync(validImages);

            // Act
            var result = await _validator.ValidateDataIntegrityAsync("B2", 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(0, result.ErrorCount);
            Assert.AreEqual(0, result.WarningCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Success", result.ValidationResults[0].Status);
        }

        [TestMethod]
        public async Task ValidateDataIntegrityAsync_WithMissingB2Data_ShouldFailValidation()
        {
            // Arrange
            var invalidImages = new List<CatGalleryImage> { _testImages[1] };
            _mockGalleryRepository.Setup(x => x.GetMigrationCandidatesAsync("S3", It.IsAny<int>()))
                .ReturnsAsync(invalidImages);

            // Act
            var result = await _validator.ValidateDataIntegrityAsync("S3", 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(1, result.ErrorCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Error", result.ValidationResults[0].Status);
            Assert.IsTrue(result.ValidationResults[0].ErrorMessage.Contains("Missing B2 storage data"));
        }

        [TestMethod]
        public async Task ValidateDataIntegrityAsync_WithEmptyResults_ShouldPassValidation()
        {
            // Arrange
            _mockGalleryRepository.Setup(x => x.GetMigrationCandidatesAsync("S3", It.IsAny<int>()))
                .ReturnsAsync(new List<CatGalleryImage>());

            // Act
            var result = await _validator.ValidateDataIntegrityAsync("S3", 10);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(0, result.ErrorCount);
            Assert.AreEqual(0, result.WarningCount);
            Assert.AreEqual(0, result.ValidationResults.Count);
        }

        [TestMethod]
        public async Task ValidateStorageConsistencyAsync_WithConsistentData_ShouldPassValidation()
        {
            // Arrange
            var consistentImages = new List<CatGalleryImage> { _testImages[0] };
            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), false, false))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = consistentImages,
                    TotalCount = 1
                });

            // Act
            var result = await _validator.ValidateStorageConsistencyAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(0, result.ErrorCount);
            Assert.AreEqual(0, result.WarningCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Success", result.ValidationResults[0].Status);
        }

        [TestMethod]
        public async Task ValidateStorageConsistencyAsync_WithInconsistentData_ShouldFailValidation()
        {
            // Arrange
            var inconsistentImage = new CatGalleryImage
            {
                Id = 3,
                Filename = "test3.jpg",
                StorageKey = "cats/test3.jpg",
                StorageProvider = "B2",
                B2Key = "cats/test3.jpg",
                B2Bucket = "test-bucket-b2",
                S3Key = "cats/different-key.jpg", // Inconsistent key
                S3Bucket = "test-bucket",
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), false, false))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = new List<CatGalleryImage> { inconsistentImage },
                    TotalCount = 1
                });

            // Act
            var result = await _validator.ValidateStorageConsistencyAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(1, result.ErrorCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Error", result.ValidationResults[0].Status);
            Assert.IsTrue(result.ValidationResults[0].ErrorMessage.Contains("Storage key mismatch"));
        }

        [TestMethod]
        public async Task ValidateMigrationCompletenessAsync_WithCompleteData_ShouldPassValidation()
        {
            // Arrange
            var completeImages = new List<CatGalleryImage> { _testImages[0] };
            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), false, false))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = completeImages,
                    TotalCount = 1
                });

            // Act
            var result = await _validator.ValidateMigrationCompletenessAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(0, result.ErrorCount);
            Assert.AreEqual(0, result.WarningCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Success", result.ValidationResults[0].Status);
        }

        [TestMethod]
        public async Task ValidateMigrationCompletenessAsync_WithIncompleteData_ShouldFailValidation()
        {
            // Arrange
            var incompleteImage = new CatGalleryImage
            {
                Id = 4,
                Filename = "test4.jpg",
                StorageKey = "cats/test4.jpg",
                StorageProvider = "S3",
                S3Key = "cats/test4.jpg",
                S3Bucket = "test-bucket",
                B2Key = null, // Missing B2 migration data
                B2Bucket = null,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), false, false))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = new List<CatGalleryImage> { incompleteImage },
                    TotalCount = 1
                });

            // Act
            var result = await _validator.ValidateMigrationCompletenessAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(1, result.ErrorCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Error", result.ValidationResults[0].Status);
            Assert.IsTrue(result.ValidationResults[0].ErrorMessage.Contains("Missing B2 storage data"));
        }

        [TestMethod]
        public async Task ValidateFileIntegrityAsync_WithValidFiles_ShouldPassValidation()
        {
            // Arrange
            var validImages = new List<CatGalleryImage> { _testImages[0] };
            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), false, false))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = validImages,
                    TotalCount = 1
                });

            // Act
            var result = await _validator.ValidateFileIntegrityAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(0, result.ErrorCount);
            Assert.AreEqual(0, result.WarningCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Success", result.ValidationResults[0].Status);
        }

        [TestMethod]
        public async Task ValidateFileIntegrityAsync_WithInvalidFiles_ShouldFailValidation()
        {
            // Arrange
            var invalidImage = new CatGalleryImage
            {
                Id = 5,
                Filename = "test5.jpg",
                StorageKey = "cats/test5.jpg",
                FileSize = 0, // Invalid file size
                StorageProvider = "S3",
                S3Key = "cats/test5.jpg",
                S3Bucket = "test-bucket",
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), false, false))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = new List<CatGalleryImage> { invalidImage },
                    TotalCount = 1
                });

            // Act
            var result = await _validator.ValidateFileIntegrityAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsValid);
            Assert.AreEqual(1, result.ErrorCount);
            Assert.AreEqual(1, result.ValidationResults.Count);
            Assert.AreEqual("Error", result.ValidationResults[0].Status);
            Assert.IsTrue(result.ValidationResults[0].ErrorMessage.Contains("Invalid file size"));
        }

        [TestMethod]
        public async Task ValidatePerformanceAsync_WithGoodPerformance_ShouldPassValidation()
        {
            // Arrange
            var fastImages = new List<CatGalleryImage> { _testImages[0] };
            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), true, true))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = fastImages,
                    TotalCount = 1
                });

            // Act
            var result = await _validator.ValidatePerformanceAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsValid);
            Assert.AreEqual(0, result.ErrorCount);
            Assert.AreEqual(0, result.WarningCount);
            Assert.IsTrue(result.ValidationResults.Any(r => r.Status == "Success"));
        }

        [TestMethod]
        public async Task ValidatePerformanceAsync_WithSlowPerformance_ShouldGenerateWarning()
        {
            // Arrange
            var slowImages = Enumerable.Range(1, 1000).Select(i => new CatGalleryImage
            {
                Id = i,
                Filename = $"test{i}.jpg",
                StorageKey = $"cats/test{i}.jpg",
                StorageProvider = "S3",
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            }).ToList();

            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), true, true))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = slowImages,
                    TotalCount = 1000
                });

            // Act
            var result = await _validator.ValidatePerformanceAsync();

            // Assert
            Assert.IsNotNull(result);
            // Performance validation might generate warnings for slow queries
            Assert.IsTrue(result.ValidationResults.Any());
        }

        [TestMethod]
        public async Task GenerateValidationReportAsync_WithMixedResults_ShouldGenerateComprehensiveReport()
        {
            // Arrange
            var mixedImages = new List<CatGalleryImage> { _testImages[0], _testImages[1] };
            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), false, false))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = mixedImages,
                    TotalCount = 2
                });

            // Act
            var result = await _validator.GenerateValidationReportAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.ValidationResults.Any());
            Assert.IsTrue(result.TotalValidated > 0);
            Assert.IsNotNull(result.Summary);
            Assert.IsTrue(result.Summary.Length > 0);
        }

        [TestMethod]
        public async Task QuickHealthCheckAsync_WithHealthySystem_ShouldPassHealthCheck()
        {
            // Arrange
            var healthyImages = new List<CatGalleryImage> { _testImages[0] };
            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), true, true))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = healthyImages,
                    TotalCount = 1
                });

            // Act
            var result = await _validator.QuickHealthCheckAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsHealthy);
            Assert.AreEqual(0, result.CriticalIssues);
            Assert.IsTrue(result.OverallScore > 0.8); // Should have a good health score
        }

        [TestMethod]
        public async Task QuickHealthCheckAsync_WithUnhealthySystem_ShouldFailHealthCheck()
        {
            // Arrange
            var unhealthyImages = new List<CatGalleryImage> { _testImages[1] }; // Image with missing B2 data
            _mockGalleryRepository.Setup(x => x.GetAllAsync(It.IsAny<int>(), It.IsAny<int>(), true, true))
                .ReturnsAsync(new PagedResult<CatGalleryImage>
                {
                    Items = unhealthyImages,
                    TotalCount = 1
                });

            // Act
            var result = await _validator.QuickHealthCheckAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsHealthy);
            Assert.IsTrue(result.CriticalIssues > 0);
            Assert.IsTrue(result.OverallScore < 0.8); // Should have a poor health score
        }

        [TestMethod]
        public void ValidateImage_WithValidImage_ShouldReturnSuccessResult()
        {
            // Arrange
            var validImage = _testImages[0];

            // Act
            var result = _validator.ValidateImage(validImage);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Success", result.Status);
            Assert.AreEqual("Valid", result.Severity);
            Assert.IsNull(result.ErrorMessage);
        }

        [TestMethod]
        public void ValidateImage_WithInvalidImage_ShouldReturnErrorResult()
        {
            // Arrange
            var invalidImage = new CatGalleryImage
            {
                Id = 6,
                Filename = "", // Invalid filename
                StorageKey = "cats/test6.jpg",
                StorageProvider = "S3",
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var result = _validator.ValidateImage(invalidImage);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Error", result.Status);
            Assert.AreEqual("Error", result.Severity);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Contains("Invalid filename"));
        }

        [TestMethod]
        public void ValidateImage_WithNullImage_ShouldReturnErrorResult()
        {
            // Act
            var result = _validator.ValidateImage(null);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Error", result.Status);
            Assert.AreEqual("Error", result.Severity);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Contains("Image is null"));
        }

        [TestMethod]
        public void ValidateStorageConsistency_WithConsistentImage_ShouldReturnSuccessResult()
        {
            // Arrange
            var consistentImage = _testImages[0];

            // Act
            var result = _validator.ValidateStorageConsistency(consistentImage);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Success", result.Status);
            Assert.AreEqual("Valid", result.Severity);
            Assert.IsNull(result.ErrorMessage);
        }

        [TestMethod]
        public void ValidateStorageConsistency_WithInconsistentImage_ShouldReturnErrorResult()
        {
            // Arrange
            var inconsistentImage = new CatGalleryImage
            {
                Id = 7,
                Filename = "test7.jpg",
                StorageKey = "cats/test7.jpg",
                StorageProvider = "B2",
                B2Key = "cats/different-key.jpg", // Inconsistent with StorageKey
                B2Bucket = "test-bucket",
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var result = _validator.ValidateStorageConsistency(inconsistentImage);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("Error", result.Status);
            Assert.AreEqual("Error", result.Severity);
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(result.ErrorMessage.Contains("Storage key mismatch"));
        }
    }
}
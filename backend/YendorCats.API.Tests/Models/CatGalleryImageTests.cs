using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Models
{
    [TestClass]
    public class CatGalleryImageTests
    {
        private CatGalleryImage _testImage;
        private CatImageMetadata _testMetadata;

        [TestInitialize]
        public void Setup()
        {
            _testImage = new CatGalleryImage
            {
                Id = 1,
                Filename = "test-image.jpg",
                StorageKey = "cats/test-image.jpg",
                OriginalFileName = "test-image.jpg",
                FileSize = 1024576,
                ContentType = "image/jpeg",
                MimeType = "image/jpeg",
                Format = "jpg",
                Width = 1920,
                Height = 1080,
                CatName = "Test Cat",
                CatId = "test-cat-1",
                Category = "gallery",
                Title = "Test Image",
                Description = "A test image for unit testing",
                Tags = "test,unit,gallery",
                Breed = "Maine Coon",
                Gender = "M",
                StorageProvider = "S3",
                StorageBucketName = "test-bucket",
                S3Key = "cats/test-image.jpg",
                S3Bucket = "test-bucket",
                S3Url = "https://test-bucket.s3.amazonaws.com/cats/test-image.jpg",
                IsActive = true,
                IsPublic = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _testMetadata = new CatImageMetadata
            {
                Name = "Test Cat",
                Gender = "M",
                FileSize = 1024576,
                ContentType = "image/jpeg",
                Width = 1920,
                Height = 1080,
                Description = "A test image for unit testing",
                Breed = "Maine Coon",
                Category = "gallery",
                Tags = "test,unit,gallery",
                DateUploaded = DateTime.UtcNow,
                DateTaken = DateTime.UtcNow.AddDays(-7),
                CatId = 1,
                RegisteredName = "Test Image"
            };
        }

        [TestMethod]
        public void CatGalleryImage_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var image = new CatGalleryImage();

            // Assert
            Assert.AreEqual(string.Empty, image.Filename);
            Assert.AreEqual(string.Empty, image.StorageKey);
            Assert.AreEqual(string.Empty, image.OriginalFileName);
            Assert.AreEqual("image/jpeg", image.ContentType);
            Assert.AreEqual("image/jpeg", image.MimeType);
            Assert.AreEqual("jpg", image.Format);
            Assert.AreEqual(string.Empty, image.Category);
            Assert.AreEqual("Maine Coon", image.Breed);
            Assert.AreEqual("S3", image.StorageProvider);
            Assert.AreEqual(string.Empty, image.StorageBucketName);
            Assert.IsTrue(image.IsActive);
            Assert.IsTrue(image.IsPublic);
            Assert.IsFalse(image.IsFeatured);
            Assert.AreEqual(0, image.ViewCount);
            Assert.AreEqual(0, image.LikeCount);
            Assert.AreEqual(0, image.DownloadCount);
            Assert.AreEqual(0, image.AccessCount);
        }

        [TestMethod]
        public void PublicUrl_WithS3Provider_ShouldReturnS3Url()
        {
            // Arrange
            _testImage.StorageProvider = "S3";
            _testImage.S3Url = "https://test-bucket.s3.amazonaws.com/cats/test-image.jpg";

            // Act
            var publicUrl = _testImage.PublicUrl;

            // Assert
            Assert.AreEqual("https://test-bucket.s3.amazonaws.com/cats/test-image.jpg", publicUrl);
        }

        [TestMethod]
        public void PublicUrl_WithS3ProviderAndNoUrl_ShouldGenerateFromKeyAndBucket()
        {
            // Arrange
            _testImage.StorageProvider = "S3";
            _testImage.S3Url = null;
            _testImage.S3Key = "cats/test-image.jpg";
            _testImage.S3Bucket = "test-bucket";

            // Act
            var publicUrl = _testImage.PublicUrl;

            // Assert
            Assert.AreEqual("https://test-bucket.s3.amazonaws.com/cats/test-image.jpg", publicUrl);
        }

        [TestMethod]
        public void PublicUrl_WithB2Provider_ShouldReturnB2Url()
        {
            // Arrange
            _testImage.StorageProvider = "B2";
            _testImage.B2Url = "https://f002.backblazeb2.com/file/test-bucket/cats/test-image.jpg";

            // Act
            var publicUrl = _testImage.PublicUrl;

            // Assert
            Assert.AreEqual("https://f002.backblazeb2.com/file/test-bucket/cats/test-image.jpg", publicUrl);
        }

        [TestMethod]
        public void PublicUrl_WithB2ProviderAndNoUrl_ShouldGenerateFromKeyAndBucket()
        {
            // Arrange
            _testImage.StorageProvider = "B2";
            _testImage.B2Url = null;
            _testImage.B2Key = "cats/test-image.jpg";
            _testImage.B2Bucket = "test-bucket";

            // Act
            var publicUrl = _testImage.PublicUrl;

            // Assert
            Assert.AreEqual("https://f002.backblazeb2.com/file/test-bucket/cats/test-image.jpg", publicUrl);
        }

        [TestMethod]
        public void PublicUrl_WithUnknownProvider_ShouldReturnEmpty()
        {
            // Arrange
            _testImage.StorageProvider = "Unknown";

            // Act
            var publicUrl = _testImage.PublicUrl;

            // Assert
            Assert.AreEqual(string.Empty, publicUrl);
        }

        [TestMethod]
        public void FromMetadata_WithValidMetadata_ShouldCreateCorrectImage()
        {
            // Act
            var image = CatGalleryImage.FromMetadata(_testMetadata, "cats/test-image.jpg", "test-bucket", "S3");

            // Assert
            Assert.AreEqual("cats/test-image.jpg", image.StorageKey);
            Assert.AreEqual("test-bucket", image.StorageBucketName);
            Assert.AreEqual("S3", image.StorageProvider);
            Assert.AreEqual("test-image.jpg", image.OriginalFileName);
            Assert.AreEqual(1024576, image.FileSize);
            Assert.AreEqual("image/jpeg", image.ContentType);
            Assert.AreEqual(1920, image.Width);
            Assert.AreEqual(1080, image.Height);
            Assert.AreEqual("Test Cat", image.CatName);
            Assert.AreEqual("1", image.CatId);
            Assert.AreEqual("gallery", image.Category);
            Assert.AreEqual("Test Image", image.Title);
            Assert.AreEqual("A test image for unit testing", image.Description);
            Assert.AreEqual("test,unit,gallery", image.Tags);
            Assert.AreEqual("Maine Coon", image.Breed);
            Assert.AreEqual("M", image.Gender);
            Assert.AreEqual("cats/test-image.jpg", image.S3Key);
            Assert.AreEqual("test-bucket", image.S3Bucket);
            Assert.AreEqual("MIGRATION", image.CreatedBy);
        }

        [TestMethod]
        public void FromMetadata_WithB2Provider_ShouldSetB2Fields()
        {
            // Act
            var image = CatGalleryImage.FromMetadata(_testMetadata, "cats/test-image.jpg", "test-bucket", "B2");

            // Assert
            Assert.AreEqual("B2", image.StorageProvider);
            Assert.AreEqual("cats/test-image.jpg", image.B2Key);
            Assert.AreEqual("test-bucket", image.B2Bucket);
            Assert.IsNull(image.S3Key);
            Assert.IsNull(image.S3Bucket);
        }

        [TestMethod]
        public void ToMetadata_WithValidImage_ShouldCreateCorrectMetadata()
        {
            // Act
            var metadata = _testImage.ToMetadata();

            // Assert
            Assert.AreEqual("Test Cat", metadata.Name);
            Assert.AreEqual("M", metadata.Gender);
            Assert.AreEqual(1024576, metadata.FileSize);
            Assert.AreEqual("image/jpeg", metadata.ContentType);
            Assert.AreEqual(1920, metadata.Width);
            Assert.AreEqual(1080, metadata.Height);
            Assert.AreEqual("A test image for unit testing", metadata.Description);
            Assert.AreEqual("Maine Coon", metadata.Breed);
            Assert.AreEqual("gallery", metadata.Category);
            Assert.AreEqual("test,unit,gallery", metadata.Tags);
            Assert.AreEqual("Test Image", metadata.RegisteredName);
            Assert.AreEqual(".jpg", metadata.FileFormat);
        }

        [TestMethod]
        public void ToMetadata_WithValidCatId_ShouldParseCorrectly()
        {
            // Arrange
            _testImage.CatId = "123";

            // Act
            var metadata = _testImage.ToMetadata();

            // Assert
            Assert.AreEqual(123, metadata.CatId);
        }

        [TestMethod]
        public void ToMetadata_WithInvalidCatId_ShouldReturnNull()
        {
            // Arrange
            _testImage.CatId = "invalid";

            // Act
            var metadata = _testImage.ToMetadata();

            // Assert
            Assert.IsNull(metadata.CatId);
        }

        [TestMethod]
        public void MigrateToB2_WithValidParameters_ShouldUpdateFields()
        {
            // Arrange
            var originalS3Key = _testImage.S3Key;
            var originalS3Bucket = _testImage.S3Bucket;
            var originalModified = _testImage.DateModified;

            // Act
            _testImage.MigrateToB2("cats/b2-test-image.jpg", "test-b2-bucket", "file123");

            // Assert
            Assert.AreEqual("B2", _testImage.StorageProvider);
            Assert.AreEqual("cats/b2-test-image.jpg", _testImage.StorageKey);
            Assert.AreEqual("test-b2-bucket", _testImage.StorageBucketName);
            Assert.AreEqual("file123", _testImage.StorageFileId);
            Assert.AreEqual("cats/b2-test-image.jpg", _testImage.B2Key);
            Assert.AreEqual("test-b2-bucket", _testImage.B2Bucket);
            Assert.AreEqual("file123", _testImage.B2FileId);
            
            // Should preserve S3 data for rollback
            Assert.AreEqual(originalS3Key, _testImage.S3Key);
            Assert.AreEqual(originalS3Bucket, _testImage.S3Bucket);
            
            // Should update modification date
            Assert.IsTrue(_testImage.DateModified > originalModified);
        }

        [TestMethod]
        public void RollbackToS3_WithValidS3Data_ShouldUpdateFields()
        {
            // Arrange
            _testImage.MigrateToB2("cats/b2-test-image.jpg", "test-b2-bucket", "file123");
            var originalModified = _testImage.DateModified;

            // Act
            _testImage.RollbackToS3();

            // Assert
            Assert.AreEqual("S3", _testImage.StorageProvider);
            Assert.AreEqual("cats/test-image.jpg", _testImage.StorageKey);
            Assert.AreEqual("test-bucket", _testImage.StorageBucketName);
            Assert.IsNull(_testImage.StorageFileId);
            
            // Should update modification date
            Assert.IsTrue(_testImage.DateModified > originalModified);
        }

        [TestMethod]
        public void RollbackToS3_WithNoS3Data_ShouldNotUpdate()
        {
            // Arrange
            _testImage.S3Key = null;
            _testImage.S3Bucket = null;
            _testImage.MigrateToB2("cats/b2-test-image.jpg", "test-b2-bucket", "file123");
            var originalProvider = _testImage.StorageProvider;

            // Act
            _testImage.RollbackToS3();

            // Assert
            Assert.AreEqual("B2", _testImage.StorageProvider); // Should remain unchanged
        }

        [TestMethod]
        public void IncrementAccessCount_ShouldUpdateCountAndTimestamp()
        {
            // Arrange
            var originalCount = _testImage.AccessCount;
            var originalAccessed = _testImage.LastAccessedAt;

            // Act
            _testImage.IncrementAccessCount();

            // Assert
            Assert.AreEqual(originalCount + 1, _testImage.AccessCount);
            Assert.IsTrue(_testImage.LastAccessedAt > originalAccessed);
        }

        [TestMethod]
        public void UpdateLastAccessed_ShouldUpdateTimestamp()
        {
            // Arrange
            var originalAccessed = _testImage.LastAccessedAt;

            // Act
            _testImage.UpdateLastAccessed();

            // Assert
            Assert.IsTrue(_testImage.LastAccessedAt > originalAccessed);
        }

        [TestMethod]
        public void ValidationAttributes_ShouldBeAppliedCorrectly()
        {
            // Arrange
            var image = new CatGalleryImage();

            // Act & Assert - Test required fields
            Assert.ThrowsException<ValidationException>(() => 
                Validator.ValidateObject(image, new ValidationContext(image), true));

            // Set required fields and validate
            image.Filename = "test.jpg";
            image.StorageKey = "cats/test.jpg";
            image.OriginalFileName = "test.jpg";
            image.Category = "gallery";
            image.StorageBucketName = "test-bucket";

            // Should not throw exception now
            Validator.ValidateObject(image, new ValidationContext(image), true);
        }

        [TestMethod]
        public void MaxLengthAttributes_ShouldBeRespected()
        {
            // Arrange
            var image = new CatGalleryImage
            {
                Filename = new string('a', 256), // Exceeds 255 max length
                StorageKey = "cats/test.jpg",
                OriginalFileName = "test.jpg",
                Category = "gallery",
                StorageBucketName = "test-bucket"
            };

            // Act & Assert
            Assert.ThrowsException<ValidationException>(() => 
                Validator.ValidateObject(image, new ValidationContext(image), true));
        }
    }
}
using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Models
{
    [TestClass]
    public class B2SyncLogTests
    {
        private B2SyncLog _testLog;

        [TestInitialize]
        public void Setup()
        {
            _testLog = new B2SyncLog
            {
                Id = 1,
                Operation = "Upload",
                SourceStorageProvider = "S3",
                SourceKey = "cats/test-image.jpg",
                SourceBucket = "source-bucket",
                DestinationStorageProvider = "B2",
                DestinationKey = "cats/test-image.jpg",
                DestinationBucket = "destination-bucket",
                DestinationFileId = "file123",
                FileSize = 1024576,
                ContentType = "image/jpeg",
                Status = "Completed",
                StartTime = DateTime.UtcNow.AddMinutes(-5),
                EndTime = DateTime.UtcNow,
                AttemptCount = 1,
                MaxRetries = 3,
                CreatedAt = DateTime.UtcNow.AddMinutes(-5),
                UpdatedAt = DateTime.UtcNow
            };
        }

        [TestMethod]
        public void B2SyncLog_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var log = new B2SyncLog();

            // Assert
            Assert.AreEqual(string.Empty, log.Operation);
            Assert.AreEqual(string.Empty, log.SourceStorageProvider);
            Assert.AreEqual(string.Empty, log.SourceKey);
            Assert.AreEqual(string.Empty, log.SourceBucket);
            Assert.AreEqual(string.Empty, log.DestinationStorageProvider);
            Assert.AreEqual(string.Empty, log.DestinationKey);
            Assert.AreEqual(string.Empty, log.DestinationBucket);
            Assert.AreEqual("Pending", log.Status);
            Assert.AreEqual(0, log.AttemptCount);
            Assert.AreEqual(3, log.MaxRetries);
            Assert.AreEqual(0, log.FileSize);
            Assert.AreEqual("application/octet-stream", log.ContentType);
        }

        [TestMethod]
        public void Duration_WithValidStartAndEndTime_ShouldCalculateCorrectly()
        {
            // Arrange
            var startTime = DateTime.UtcNow.AddMinutes(-5);
            var endTime = DateTime.UtcNow;
            _testLog.StartTime = startTime;
            _testLog.EndTime = endTime;

            // Act
            var duration = _testLog.Duration;

            // Assert
            Assert.IsTrue(duration.HasValue);
            Assert.IsTrue(duration.Value.TotalMinutes >= 4.9 && duration.Value.TotalMinutes <= 5.1);
        }

        [TestMethod]
        public void Duration_WithNullEndTime_ShouldReturnNull()
        {
            // Arrange
            _testLog.EndTime = null;

            // Act
            var duration = _testLog.Duration;

            // Assert
            Assert.IsNull(duration);
        }

        [TestMethod]
        public void Duration_WithNullStartTime_ShouldReturnNull()
        {
            // Arrange
            _testLog.StartTime = null;

            // Act
            var duration = _testLog.Duration;

            // Assert
            Assert.IsNull(duration);
        }

        [TestMethod]
        public void IsCompleted_WithCompletedStatus_ShouldReturnTrue()
        {
            // Arrange
            _testLog.Status = "Completed";

            // Act
            var isCompleted = _testLog.IsCompleted;

            // Assert
            Assert.IsTrue(isCompleted);
        }

        [TestMethod]
        public void IsCompleted_WithFailedStatus_ShouldReturnFalse()
        {
            // Arrange
            _testLog.Status = "Failed";

            // Act
            var isCompleted = _testLog.IsCompleted;

            // Assert
            Assert.IsFalse(isCompleted);
        }

        [TestMethod]
        public void IsCompleted_WithPendingStatus_ShouldReturnFalse()
        {
            // Arrange
            _testLog.Status = "Pending";

            // Act
            var isCompleted = _testLog.IsCompleted;

            // Assert
            Assert.IsFalse(isCompleted);
        }

        [TestMethod]
        public void IsCompleted_WithProcessingStatus_ShouldReturnFalse()
        {
            // Arrange
            _testLog.Status = "Processing";

            // Act
            var isCompleted = _testLog.IsCompleted;

            // Assert
            Assert.IsFalse(isCompleted);
        }

        [TestMethod]
        public void IsFailed_WithFailedStatus_ShouldReturnTrue()
        {
            // Arrange
            _testLog.Status = "Failed";

            // Act
            var isFailed = _testLog.IsFailed;

            // Assert
            Assert.IsTrue(isFailed);
        }

        [TestMethod]
        public void IsFailed_WithCompletedStatus_ShouldReturnFalse()
        {
            // Arrange
            _testLog.Status = "Completed";

            // Act
            var isFailed = _testLog.IsFailed;

            // Assert
            Assert.IsFalse(isFailed);
        }

        [TestMethod]
        public void CanRetry_WithFailedStatusAndRetriesRemaining_ShouldReturnTrue()
        {
            // Arrange
            _testLog.Status = "Failed";
            _testLog.AttemptCount = 2;
            _testLog.MaxRetries = 3;

            // Act
            var canRetry = _testLog.CanRetry;

            // Assert
            Assert.IsTrue(canRetry);
        }

        [TestMethod]
        public void CanRetry_WithFailedStatusAndNoRetriesRemaining_ShouldReturnFalse()
        {
            // Arrange
            _testLog.Status = "Failed";
            _testLog.AttemptCount = 3;
            _testLog.MaxRetries = 3;

            // Act
            var canRetry = _testLog.CanRetry;

            // Assert
            Assert.IsFalse(canRetry);
        }

        [TestMethod]
        public void CanRetry_WithCompletedStatus_ShouldReturnFalse()
        {
            // Arrange
            _testLog.Status = "Completed";
            _testLog.AttemptCount = 1;
            _testLog.MaxRetries = 3;

            // Act
            var canRetry = _testLog.CanRetry;

            // Assert
            Assert.IsFalse(canRetry);
        }

        [TestMethod]
        public void CanRetry_WithPendingStatus_ShouldReturnFalse()
        {
            // Arrange
            _testLog.Status = "Pending";
            _testLog.AttemptCount = 1;
            _testLog.MaxRetries = 3;

            // Act
            var canRetry = _testLog.CanRetry;

            // Assert
            Assert.IsFalse(canRetry);
        }

        [TestMethod]
        public void RemainingRetries_WithRetriesRemaining_ShouldReturnCorrectValue()
        {
            // Arrange
            _testLog.AttemptCount = 2;
            _testLog.MaxRetries = 5;

            // Act
            var remainingRetries = _testLog.RemainingRetries;

            // Assert
            Assert.AreEqual(3, remainingRetries);
        }

        [TestMethod]
        public void RemainingRetries_WithNoRetriesRemaining_ShouldReturnZero()
        {
            // Arrange
            _testLog.AttemptCount = 3;
            _testLog.MaxRetries = 3;

            // Act
            var remainingRetries = _testLog.RemainingRetries;

            // Assert
            Assert.AreEqual(0, remainingRetries);
        }

        [TestMethod]
        public void RemainingRetries_WithMoreAttemptsThanMax_ShouldReturnZero()
        {
            // Arrange
            _testLog.AttemptCount = 5;
            _testLog.MaxRetries = 3;

            // Act
            var remainingRetries = _testLog.RemainingRetries;

            // Assert
            Assert.AreEqual(0, remainingRetries);
        }

        [TestMethod]
        public void MarkAsStarted_ShouldUpdateStatusAndStartTime()
        {
            // Arrange
            _testLog.Status = "Pending";
            _testLog.StartTime = null;

            // Act
            _testLog.MarkAsStarted();

            // Assert
            Assert.AreEqual("Processing", _testLog.Status);
            Assert.IsNotNull(_testLog.StartTime);
            Assert.IsTrue(_testLog.StartTime.Value > DateTime.UtcNow.AddMinutes(-1));
        }

        [TestMethod]
        public void MarkAsCompleted_ShouldUpdateStatusAndEndTime()
        {
            // Arrange
            _testLog.Status = "Processing";
            _testLog.EndTime = null;

            // Act
            _testLog.MarkAsCompleted();

            // Assert
            Assert.AreEqual("Completed", _testLog.Status);
            Assert.IsNotNull(_testLog.EndTime);
            Assert.IsTrue(_testLog.EndTime.Value > DateTime.UtcNow.AddMinutes(-1));
        }

        [TestMethod]
        public void MarkAsFailed_ShouldUpdateStatusEndTimeAndError()
        {
            // Arrange
            _testLog.Status = "Processing";
            _testLog.EndTime = null;
            _testLog.ErrorMessage = null;
            var errorMessage = "Test error message";

            // Act
            _testLog.MarkAsFailed(errorMessage);

            // Assert
            Assert.AreEqual("Failed", _testLog.Status);
            Assert.IsNotNull(_testLog.EndTime);
            Assert.IsTrue(_testLog.EndTime.Value > DateTime.UtcNow.AddMinutes(-1));
            Assert.AreEqual(errorMessage, _testLog.ErrorMessage);
        }

        [TestMethod]
        public void MarkAsFailed_WithNullErrorMessage_ShouldUpdateStatusAndEndTime()
        {
            // Arrange
            _testLog.Status = "Processing";
            _testLog.EndTime = null;

            // Act
            _testLog.MarkAsFailed(null);

            // Assert
            Assert.AreEqual("Failed", _testLog.Status);
            Assert.IsNotNull(_testLog.EndTime);
            Assert.IsNull(_testLog.ErrorMessage);
        }

        [TestMethod]
        public void IncrementAttempt_ShouldUpdateAttemptCount()
        {
            // Arrange
            var originalAttemptCount = _testLog.AttemptCount;

            // Act
            _testLog.IncrementAttempt();

            // Assert
            Assert.AreEqual(originalAttemptCount + 1, _testLog.AttemptCount);
        }

        [TestMethod]
        public void SetDestination_ShouldUpdateDestinationFields()
        {
            // Arrange
            var key = "new-cats/test-image.jpg";
            var bucket = "new-destination-bucket";
            var fileId = "newfile456";

            // Act
            _testLog.SetDestination(key, bucket, fileId);

            // Assert
            Assert.AreEqual(key, _testLog.DestinationKey);
            Assert.AreEqual(bucket, _testLog.DestinationBucket);
            Assert.AreEqual(fileId, _testLog.DestinationFileId);
        }

        [TestMethod]
        public void SetDestination_WithNullFileId_ShouldUpdateKeyAndBucket()
        {
            // Arrange
            var key = "new-cats/test-image.jpg";
            var bucket = "new-destination-bucket";

            // Act
            _testLog.SetDestination(key, bucket, null);

            // Assert
            Assert.AreEqual(key, _testLog.DestinationKey);
            Assert.AreEqual(bucket, _testLog.DestinationBucket);
            Assert.IsNull(_testLog.DestinationFileId);
        }

        [TestMethod]
        public void ValidationAttributes_ShouldBeAppliedCorrectly()
        {
            // Arrange
            var log = new B2SyncLog();

            // Act & Assert - Test required fields
            Assert.ThrowsException<ValidationException>(() => 
                Validator.ValidateObject(log, new ValidationContext(log), true));

            // Set required fields and validate
            log.Operation = "Upload";
            log.SourceStorageProvider = "S3";
            log.SourceKey = "cats/test.jpg";
            log.SourceBucket = "source-bucket";
            log.DestinationStorageProvider = "B2";
            log.DestinationKey = "cats/test.jpg";
            log.DestinationBucket = "destination-bucket";

            // Should not throw exception now
            Validator.ValidateObject(log, new ValidationContext(log), true);
        }

        [TestMethod]
        public void MaxLengthAttributes_ShouldBeRespected()
        {
            // Arrange
            var log = new B2SyncLog
            {
                Operation = new string('a', 51), // Exceeds 50 max length
                SourceStorageProvider = "S3",
                SourceKey = "cats/test.jpg",
                SourceBucket = "source-bucket",
                DestinationStorageProvider = "B2",
                DestinationKey = "cats/test.jpg",
                DestinationBucket = "destination-bucket"
            };

            // Act & Assert
            Assert.ThrowsException<ValidationException>(() => 
                Validator.ValidateObject(log, new ValidationContext(log), true));
        }

        [TestMethod]
        public void Status_WithValidValues_ShouldBeAccepted()
        {
            // Arrange & Act & Assert
            var validStatuses = new[] { "Pending", "Processing", "Completed", "Failed" };
            
            foreach (var status in validStatuses)
            {
                _testLog.Status = status;
                // Should not throw exception
                Validator.ValidateObject(_testLog, new ValidationContext(_testLog), true);
            }
        }

        [TestMethod]
        public void AttemptCount_ShouldNotBeNegative()
        {
            // Arrange
            _testLog.AttemptCount = -1;

            // Act & Assert
            // Note: This would require a Range validation attribute to enforce
            // For now, we'll just verify the computed property handles it correctly
            Assert.AreEqual(_testLog.MaxRetries, _testLog.RemainingRetries);
        }

        [TestMethod]
        public void FileSize_ShouldNotBeNegative()
        {
            // Arrange
            _testLog.FileSize = -1;

            // Act & Assert
            // Note: This would require a Range validation attribute to enforce
            // For now, we'll just verify the value is set
            Assert.AreEqual(-1, _testLog.FileSize);
        }

        [TestMethod]
        public void UpdatedAt_ShouldBeSetOnModification()
        {
            // Arrange
            var originalUpdatedAt = _testLog.UpdatedAt;

            // Act
            _testLog.Status = "Updated Status";
            _testLog.UpdatedAt = DateTime.UtcNow;

            // Assert
            Assert.IsTrue(_testLog.UpdatedAt > originalUpdatedAt);
        }
    }
}
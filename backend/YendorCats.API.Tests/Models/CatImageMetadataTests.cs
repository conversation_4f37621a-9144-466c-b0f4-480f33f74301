using System;
using System.Collections.Generic;
using Xunit;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Models
{
    public class CatImageMetadataTests
    {
        [Fact]
        public void ToS3Metadata_AllFields_MapsCorrectly()
        {
            // Arrange
            var metadata = new CatImageMetadata
            {
                Name = "Fluffy",
                Age = "2.5",
                DateTaken = new DateTime(2023, 5, 15),
                Description = "Beautiful Persian cat",
                Breed = "Persian",
                Gender = "F",
                HairColor = "White",
                Personality = "Calm and friendly",
                Bloodline = "Champion bloodline",
                CatId = 123,
                RegisteredName = "CH Fluffy of Yendor",
                RegistrationNumber = "REG123456",
                FatherCatId = 456,
                MotherCatId = 789,
                BreedingStatus = "available-kitten",
                AvailabilityStatus = "available",
                PhotoType = "profile",
                AgeAtPhoto = "2.5 years",
                Tags = "fluffy,persian,white",
                ChampionTitles = "Grand Champion",
                GenerationLevel = "1",
                DateUploaded = new DateTime(2023, 6, 1),
                FileFormat = "jpg",
                ContentType = "image/jpeg"
            };

            // Act
            var s3Metadata = metadata.ToS3Metadata();

            // Assert
            Assert.Equal("Fluffy", s3Metadata["cat-name"]);
            Assert.Equal("2.5", s3Metadata["cat-age"]);
            Assert.Equal("2023-05-15", s3Metadata["date-taken"]);
            Assert.Equal("Beautiful Persian cat", s3Metadata["description"]);
            Assert.Equal("Persian", s3Metadata["breed"]);
            Assert.Equal("F", s3Metadata["gender"]);
            Assert.Equal("White", s3Metadata["hair-color"]);
            Assert.Equal("Calm and friendly", s3Metadata["personality"]);
            Assert.Equal("Champion bloodline", s3Metadata["bloodline"]);
            Assert.Equal("123", s3Metadata["cat-id"]);
            Assert.Equal("CH Fluffy of Yendor", s3Metadata["registered-name"]);
            Assert.Equal("REG123456", s3Metadata["registration-number"]);
            Assert.Equal("456", s3Metadata["father-cat-id"]);
            Assert.Equal("789", s3Metadata["mother-cat-id"]);
            Assert.Equal("available-kitten", s3Metadata["breeding-status"]);
            Assert.Equal("available", s3Metadata["availability-status"]);
            Assert.Equal("profile", s3Metadata["photo-type"]);
            Assert.Equal("2.5 years", s3Metadata["age-at-photo"]);
            Assert.Equal("fluffy,persian,white", s3Metadata["tags"]);
            Assert.Equal("Grand Champion", s3Metadata["champion-titles"]);
            Assert.Equal("1", s3Metadata["generation-level"]);
            Assert.Equal("2023-06-01T00:00:00Z", s3Metadata["date-uploaded"]);
            Assert.Equal("jpg", s3Metadata["file-format"]);
            Assert.Equal("image/jpeg", s3Metadata["content-type"]);
        }

        [Fact]
        public void ToS3Metadata_NullFields_HandlesGracefully()
        {
            // Arrange
            var metadata = new CatImageMetadata
            {
                Name = "Fluffy",
                Age = null,
                DateTaken = null,
                Description = null,
                CatId = null,
                FatherCatId = null,
                MotherCatId = null
            };

            // Act
            var s3Metadata = metadata.ToS3Metadata();

            // Assert
            Assert.Equal("Fluffy", s3Metadata["cat-name"]);
            Assert.Equal("", s3Metadata["cat-age"]);
            Assert.Equal("", s3Metadata["date-taken"]);
            Assert.Equal("", s3Metadata["description"]);
            Assert.Equal("", s3Metadata["cat-id"]);
            Assert.Equal("", s3Metadata["father-cat-id"]);
            Assert.Equal("", s3Metadata["mother-cat-id"]);
        }

        [Fact]
        public void FromS3Metadata_AllFields_MapsCorrectly()
        {
            // Arrange
            var s3Metadata = new Dictionary<string, string>
            {
                ["cat-name"] = "Fluffy",
                ["cat-age"] = "2.5",
                ["date-taken"] = "2023-05-15",
                ["description"] = "Beautiful Persian cat",
                ["breed"] = "Persian",
                ["gender"] = "F",
                ["hair-color"] = "White",
                ["personality"] = "Calm and friendly",
                ["bloodline"] = "Champion bloodline",
                ["cat-id"] = "123",
                ["registered-name"] = "CH Fluffy of Yendor",
                ["registration-number"] = "REG123456",
                ["father-cat-id"] = "456",
                ["mother-cat-id"] = "789",
                ["breeding-status"] = "available-kitten",
                ["availability-status"] = "available",
                ["photo-type"] = "profile",
                ["age-at-photo"] = "2.5 years",
                ["tags"] = "fluffy,persian,white",
                ["champion-titles"] = "Grand Champion",
                ["generation-level"] = "1",
                ["date-uploaded"] = "2023-06-01T00:00:00Z",
                ["file-format"] = "jpg",
                ["content-type"] = "image/jpeg"
            };

            // Act
            var metadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.Equal("Fluffy", metadata.Name);
            Assert.Equal("2.5", metadata.Age);
            Assert.Equal(new DateTime(2023, 5, 15), metadata.DateTaken);
            Assert.Equal("Beautiful Persian cat", metadata.Description);
            Assert.Equal("Persian", metadata.Breed);
            Assert.Equal("F", metadata.Gender);
            Assert.Equal("White", metadata.HairColor);
            Assert.Equal("Calm and friendly", metadata.Personality);
            Assert.Equal("Champion bloodline", metadata.Bloodline);
            Assert.Equal(123, metadata.CatId);
            Assert.Equal("CH Fluffy of Yendor", metadata.RegisteredName);
            Assert.Equal("REG123456", metadata.RegistrationNumber);
            Assert.Equal(456, metadata.FatherCatId);
            Assert.Equal(789, metadata.MotherCatId);
            Assert.Equal("available-kitten", metadata.BreedingStatus);
            Assert.Equal("available", metadata.AvailabilityStatus);
            Assert.Equal("profile", metadata.PhotoType);
            Assert.Equal("2.5 years", metadata.AgeAtPhoto);
            Assert.Equal("fluffy,persian,white", metadata.Tags);
            Assert.Equal("Grand Champion", metadata.ChampionTitles);
            Assert.Equal("1", metadata.GenerationLevel);
            Assert.Equal(new DateTime(2023, 6, 1), metadata.DateUploaded);
            Assert.Equal("jpg", metadata.FileFormat);
            Assert.Equal("image/jpeg", metadata.ContentType);
        }

        [Fact]
        public void FromS3Metadata_EmptyValues_HandlesGracefully()
        {
            // Arrange
            var s3Metadata = new Dictionary<string, string>
            {
                ["cat-name"] = "Fluffy",
                ["cat-age"] = "",
                ["date-taken"] = "",
                ["description"] = "",
                ["cat-id"] = "",
                ["father-cat-id"] = "",
                ["mother-cat-id"] = ""
            };

            // Act
            var metadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.Equal("Fluffy", metadata.Name);
            Assert.Equal("", metadata.Age);
            Assert.Null(metadata.DateTaken);
            Assert.Equal("", metadata.Description);
            Assert.Null(metadata.CatId);
            Assert.Null(metadata.FatherCatId);
            Assert.Null(metadata.MotherCatId);
        }

        [Fact]
        public void FromS3Metadata_MissingKeys_HandlesGracefully()
        {
            // Arrange
            var s3Metadata = new Dictionary<string, string>
            {
                ["cat-name"] = "Fluffy",
                ["breed"] = "Persian"
                // Missing most keys
            };

            // Act
            var metadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.Equal("Fluffy", metadata.Name);
            Assert.Equal("Persian", metadata.Breed);
            Assert.Equal("", metadata.Age);
            Assert.Equal("", metadata.Description);
            Assert.Null(metadata.CatId);
            Assert.Null(metadata.DateTaken);
        }

        [Fact]
        public void FromS3Metadata_InvalidDateFormat_HandlesGracefully()
        {
            // Arrange
            var s3Metadata = new Dictionary<string, string>
            {
                ["cat-name"] = "Fluffy",
                ["date-taken"] = "invalid-date",
                ["date-uploaded"] = "also-invalid"
            };

            // Act
            var metadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.Equal("Fluffy", metadata.Name);
            Assert.Null(metadata.DateTaken);
            Assert.Null(metadata.DateUploaded);
        }

        [Fact]
        public void FromS3Metadata_InvalidIntegerValues_HandlesGracefully()
        {
            // Arrange
            var s3Metadata = new Dictionary<string, string>
            {
                ["cat-name"] = "Fluffy",
                ["cat-id"] = "not-a-number",
                ["father-cat-id"] = "also-not-a-number",
                ["mother-cat-id"] = "definitely-not-a-number"
            };

            // Act
            var metadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.Equal("Fluffy", metadata.Name);
            Assert.Null(metadata.CatId);
            Assert.Null(metadata.FatherCatId);
            Assert.Null(metadata.MotherCatId);
        }

        [Fact]
        public void RoundTrip_ToS3AndBack_PreservesData()
        {
            // Arrange
            var originalMetadata = new CatImageMetadata
            {
                Name = "Fluffy",
                Age = "2.5",
                DateTaken = new DateTime(2023, 5, 15),
                Description = "Beautiful Persian cat",
                Breed = "Persian",
                Gender = "F",
                HairColor = "White",
                CatId = 123,
                FatherCatId = 456,
                MotherCatId = 789,
                BreedingStatus = "available-kitten",
                AvailabilityStatus = "available",
                PhotoType = "profile",
                Tags = "fluffy,persian,white",
                GenerationLevel = "1",
                DateUploaded = new DateTime(2023, 6, 1),
                FileFormat = "jpg",
                ContentType = "image/jpeg"
            };

            // Act
            var s3Metadata = originalMetadata.ToS3Metadata();
            var reconstructedMetadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.Equal(originalMetadata.Name, reconstructedMetadata.Name);
            Assert.Equal(originalMetadata.Age, reconstructedMetadata.Age);
            Assert.Equal(originalMetadata.DateTaken, reconstructedMetadata.DateTaken);
            Assert.Equal(originalMetadata.Description, reconstructedMetadata.Description);
            Assert.Equal(originalMetadata.Breed, reconstructedMetadata.Breed);
            Assert.Equal(originalMetadata.Gender, reconstructedMetadata.Gender);
            Assert.Equal(originalMetadata.HairColor, reconstructedMetadata.HairColor);
            Assert.Equal(originalMetadata.CatId, reconstructedMetadata.CatId);
            Assert.Equal(originalMetadata.FatherCatId, reconstructedMetadata.FatherCatId);
            Assert.Equal(originalMetadata.MotherCatId, reconstructedMetadata.MotherCatId);
            Assert.Equal(originalMetadata.BreedingStatus, reconstructedMetadata.BreedingStatus);
            Assert.Equal(originalMetadata.AvailabilityStatus, reconstructedMetadata.AvailabilityStatus);
            Assert.Equal(originalMetadata.PhotoType, reconstructedMetadata.PhotoType);
            Assert.Equal(originalMetadata.Tags, reconstructedMetadata.Tags);
            Assert.Equal(originalMetadata.GenerationLevel, reconstructedMetadata.GenerationLevel);
            Assert.Equal(originalMetadata.DateUploaded, reconstructedMetadata.DateUploaded);
            Assert.Equal(originalMetadata.FileFormat, reconstructedMetadata.FileFormat);
            Assert.Equal(originalMetadata.ContentType, reconstructedMetadata.ContentType);
        }

        [Fact]
        public void ToS3Metadata_EmptyMetadata_ReturnsValidDictionary()
        {
            // Arrange
            var metadata = new CatImageMetadata();

            // Act
            var s3Metadata = metadata.ToS3Metadata();

            // Assert
            Assert.NotNull(s3Metadata);
            Assert.True(s3Metadata.Count > 0);
            Assert.Equal("", s3Metadata["cat-name"]);
            Assert.Equal("", s3Metadata["cat-age"]);
            Assert.Equal("", s3Metadata["cat-id"]);
        }

        [Fact]
        public void FromS3Metadata_EmptyDictionary_ReturnsValidMetadata()
        {
            // Arrange
            var s3Metadata = new Dictionary<string, string>();

            // Act
            var metadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.NotNull(metadata);
            Assert.Equal("", metadata.Name);
            Assert.Equal("", metadata.Age);
            Assert.Null(metadata.CatId);
            Assert.Null(metadata.DateTaken);
        }

        [Fact]
        public void FromS3Metadata_NullDictionary_ReturnsValidMetadata()
        {
            // Arrange
            Dictionary<string, string> s3Metadata = null;

            // Act
            var metadata = CatImageMetadata.FromS3Metadata(s3Metadata);

            // Assert
            Assert.NotNull(metadata);
            Assert.Equal("", metadata.Name);
            Assert.Equal("", metadata.Age);
            Assert.Null(metadata.CatId);
        }
    }
}
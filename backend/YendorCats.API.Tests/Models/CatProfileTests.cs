using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using YendorCats.API.Models;

namespace YendorCats.API.Tests.Models
{
    [TestClass]
    public class CatProfileTests
    {
        private CatProfile _testProfile;

        [TestInitialize]
        public void Setup()
        {
            _testProfile = new CatProfile
            {
                Id = 1,
                Name = "Test Cat",
                RegisteredName = "CH Test Cattery's Amazing Cat",
                CatId = "test-cat-1",
                Breed = "Maine Coon",
                Gender = "M",
                DateOfBirth = new DateTime(2020, 6, 15),
                Color = "Brown Tabby",
                EyeColor = "Green",
                Weight = 12.5f,
                Height = 30.0f,
                Status = "Active",
                BreedingStatus = "Breeding",
                BloodlineType = "Foundation",
                Bloodline = "Test Bloodline",
                Pedigree = "Test Pedigree Data",
                MicrochipNumber = "123456789012345",
                RegistrationNumber = "REG123456",
                Awards = "Best in Show 2023",
                HealthRecords = "All vaccinations up to date",
                Description = "A beautiful Maine Coon cat with excellent temperament",
                IsActive = true,
                IsPublic = true,
                IsBreeding = true,
                IsStudService = true,
                IsRetired = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        [TestMethod]
        public void CatProfile_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var profile = new CatProfile();

            // Assert
            Assert.AreEqual(string.Empty, profile.Name);
            Assert.AreEqual(string.Empty, profile.CatId);
            Assert.AreEqual("Maine Coon", profile.Breed);
            Assert.AreEqual("Unknown", profile.Gender);
            Assert.AreEqual("Unknown", profile.Color);
            Assert.AreEqual("Unknown", profile.EyeColor);
            Assert.AreEqual("Active", profile.Status);
            Assert.AreEqual("Pet", profile.BreedingStatus);
            Assert.AreEqual("Foundation", profile.BloodlineType);
            Assert.IsTrue(profile.IsActive);
            Assert.IsTrue(profile.IsPublic);
            Assert.IsFalse(profile.IsBreeding);
            Assert.IsFalse(profile.IsStudService);
            Assert.IsFalse(profile.IsRetired);
            Assert.AreEqual(0, profile.ViewCount);
            Assert.AreEqual(0, profile.LikeCount);
            Assert.AreEqual(0, profile.OffspringCount);
        }

        [TestMethod]
        public void Age_WithValidDateOfBirth_ShouldCalculateCorrectly()
        {
            // Arrange
            var birthDate = DateTime.UtcNow.AddYears(-3).AddMonths(-6); // 3.5 years ago
            _testProfile.DateOfBirth = birthDate;

            // Act
            var age = _testProfile.Age;

            // Assert
            Assert.AreEqual(3, age); // Should be 3 years old
        }

        [TestMethod]
        public void Age_WithNullDateOfBirth_ShouldReturnZero()
        {
            // Arrange
            _testProfile.DateOfBirth = null;

            // Act
            var age = _testProfile.Age;

            // Assert
            Assert.AreEqual(0, age);
        }

        [TestMethod]
        public void Age_WithFutureDateOfBirth_ShouldReturnZero()
        {
            // Arrange
            _testProfile.DateOfBirth = DateTime.UtcNow.AddYears(1);

            // Act
            var age = _testProfile.Age;

            // Assert
            Assert.AreEqual(0, age);
        }

        [TestMethod]
        public void AgeInMonths_WithValidDateOfBirth_ShouldCalculateCorrectly()
        {
            // Arrange
            var birthDate = DateTime.UtcNow.AddMonths(-18); // 18 months ago
            _testProfile.DateOfBirth = birthDate;

            // Act
            var ageInMonths = _testProfile.AgeInMonths;

            // Assert
            Assert.IsTrue(ageInMonths >= 17 && ageInMonths <= 19); // Allow for some variance
        }

        [TestMethod]
        public void AgeInMonths_WithNullDateOfBirth_ShouldReturnZero()
        {
            // Arrange
            _testProfile.DateOfBirth = null;

            // Act
            var ageInMonths = _testProfile.AgeInMonths;

            // Assert
            Assert.AreEqual(0, ageInMonths);
        }

        [TestMethod]
        public void DisplayName_WithRegisteredName_ShouldReturnRegisteredName()
        {
            // Arrange
            _testProfile.RegisteredName = "CH Test Cattery's Amazing Cat";
            _testProfile.Name = "Test Cat";

            // Act
            var displayName = _testProfile.DisplayName;

            // Assert
            Assert.AreEqual("CH Test Cattery's Amazing Cat", displayName);
        }

        [TestMethod]
        public void DisplayName_WithoutRegisteredName_ShouldReturnName()
        {
            // Arrange
            _testProfile.RegisteredName = null;
            _testProfile.Name = "Test Cat";

            // Act
            var displayName = _testProfile.DisplayName;

            // Assert
            Assert.AreEqual("Test Cat", displayName);
        }

        [TestMethod]
        public void DisplayName_WithEmptyRegisteredName_ShouldReturnName()
        {
            // Arrange
            _testProfile.RegisteredName = string.Empty;
            _testProfile.Name = "Test Cat";

            // Act
            var displayName = _testProfile.DisplayName;

            // Assert
            Assert.AreEqual("Test Cat", displayName);
        }

        [TestMethod]
        public void IsKitten_WithYoungCat_ShouldReturnTrue()
        {
            // Arrange
            _testProfile.DateOfBirth = DateTime.UtcNow.AddMonths(-8); // 8 months old

            // Act
            var isKitten = _testProfile.IsKitten;

            // Assert
            Assert.IsTrue(isKitten);
        }

        [TestMethod]
        public void IsKitten_WithOldCat_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.DateOfBirth = DateTime.UtcNow.AddYears(-2); // 2 years old

            // Act
            var isKitten = _testProfile.IsKitten;

            // Assert
            Assert.IsFalse(isKitten);
        }

        [TestMethod]
        public void IsKitten_WithNullDateOfBirth_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.DateOfBirth = null;

            // Act
            var isKitten = _testProfile.IsKitten;

            // Assert
            Assert.IsFalse(isKitten);
        }

        [TestMethod]
        public void IsSenior_WithOldCat_ShouldReturnTrue()
        {
            // Arrange
            _testProfile.DateOfBirth = DateTime.UtcNow.AddYears(-9); // 9 years old

            // Act
            var isSenior = _testProfile.IsSenior;

            // Assert
            Assert.IsTrue(isSenior);
        }

        [TestMethod]
        public void IsSenior_WithYoungCat_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.DateOfBirth = DateTime.UtcNow.AddYears(-3); // 3 years old

            // Act
            var isSenior = _testProfile.IsSenior;

            // Assert
            Assert.IsFalse(isSenior);
        }

        [TestMethod]
        public void IsAvailableForBreeding_WithBreedingCat_ShouldReturnTrue()
        {
            // Arrange
            _testProfile.IsBreeding = true;
            _testProfile.IsRetired = false;
            _testProfile.IsActive = true;
            _testProfile.BreedingStatus = "Breeding";

            // Act
            var isAvailable = _testProfile.IsAvailableForBreeding;

            // Assert
            Assert.IsTrue(isAvailable);
        }

        [TestMethod]
        public void IsAvailableForBreeding_WithRetiredCat_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.IsBreeding = true;
            _testProfile.IsRetired = true;
            _testProfile.IsActive = true;
            _testProfile.BreedingStatus = "Breeding";

            // Act
            var isAvailable = _testProfile.IsAvailableForBreeding;

            // Assert
            Assert.IsFalse(isAvailable);
        }

        [TestMethod]
        public void IsAvailableForBreeding_WithInactiveCat_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.IsBreeding = true;
            _testProfile.IsRetired = false;
            _testProfile.IsActive = false;
            _testProfile.BreedingStatus = "Breeding";

            // Act
            var isAvailable = _testProfile.IsAvailableForBreeding;

            // Assert
            Assert.IsFalse(isAvailable);
        }

        [TestMethod]
        public void IsAvailableForBreeding_WithNonBreedingCat_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.IsBreeding = false;
            _testProfile.IsRetired = false;
            _testProfile.IsActive = true;
            _testProfile.BreedingStatus = "Pet";

            // Act
            var isAvailable = _testProfile.IsAvailableForBreeding;

            // Assert
            Assert.IsFalse(isAvailable);
        }

        [TestMethod]
        public void HasPedigree_WithPedigreeData_ShouldReturnTrue()
        {
            // Arrange
            _testProfile.Pedigree = "Valid pedigree data";

            // Act
            var hasPedigree = _testProfile.HasPedigree;

            // Assert
            Assert.IsTrue(hasPedigree);
        }

        [TestMethod]
        public void HasPedigree_WithNullPedigree_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.Pedigree = null;

            // Act
            var hasPedigree = _testProfile.HasPedigree;

            // Assert
            Assert.IsFalse(hasPedigree);
        }

        [TestMethod]
        public void HasPedigree_WithEmptyPedigree_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.Pedigree = string.Empty;

            // Act
            var hasPedigree = _testProfile.HasPedigree;

            // Assert
            Assert.IsFalse(hasPedigree);
        }

        [TestMethod]
        public void HasPedigree_WithWhitespacePedigree_ShouldReturnFalse()
        {
            // Arrange
            _testProfile.Pedigree = "   ";

            // Act
            var hasPedigree = _testProfile.HasPedigree;

            // Assert
            Assert.IsFalse(hasPedigree);
        }

        [TestMethod]
        public void UpdateLastActivity_ShouldUpdateTimestamp()
        {
            // Arrange
            var originalActivity = _testProfile.LastActivityAt;

            // Act
            _testProfile.UpdateLastActivity();

            // Assert
            Assert.IsTrue(_testProfile.LastActivityAt > originalActivity);
        }

        [TestMethod]
        public void IncrementViewCount_ShouldUpdateCountAndActivity()
        {
            // Arrange
            var originalViewCount = _testProfile.ViewCount;
            var originalActivity = _testProfile.LastActivityAt;

            // Act
            _testProfile.IncrementViewCount();

            // Assert
            Assert.AreEqual(originalViewCount + 1, _testProfile.ViewCount);
            Assert.IsTrue(_testProfile.LastActivityAt > originalActivity);
        }

        [TestMethod]
        public void ValidationAttributes_ShouldBeAppliedCorrectly()
        {
            // Arrange
            var profile = new CatProfile();

            // Act & Assert - Test required fields
            Assert.ThrowsException<ValidationException>(() => 
                Validator.ValidateObject(profile, new ValidationContext(profile), true));

            // Set required fields and validate
            profile.Name = "Test Cat";
            profile.CatId = "test-cat-1";

            // Should not throw exception now
            Validator.ValidateObject(profile, new ValidationContext(profile), true);
        }

        [TestMethod]
        public void MaxLengthAttributes_ShouldBeRespected()
        {
            // Arrange
            var profile = new CatProfile
            {
                Name = new string('a', 101), // Exceeds 100 max length
                CatId = "test-cat-1"
            };

            // Act & Assert
            Assert.ThrowsException<ValidationException>(() => 
                Validator.ValidateObject(profile, new ValidationContext(profile), true));
        }

        [TestMethod]
        public void MicrochipNumber_WithValidLength_ShouldBeValid()
        {
            // Arrange
            _testProfile.MicrochipNumber = "123456789012345"; // 15 digits

            // Act & Assert
            Validator.ValidateObject(_testProfile, new ValidationContext(_testProfile), true);
        }

        [TestMethod]
        public void MicrochipNumber_WithInvalidLength_ShouldBeInvalid()
        {
            // Arrange
            _testProfile.MicrochipNumber = new string('1', 16); // Exceeds 15 max length

            // Act & Assert
            Assert.ThrowsException<ValidationException>(() => 
                Validator.ValidateObject(_testProfile, new ValidationContext(_testProfile), true));
        }

        [TestMethod]
        public void DateOfBirth_WithFutureDate_ShouldBeInvalid()
        {
            // Arrange
            _testProfile.DateOfBirth = DateTime.UtcNow.AddDays(1);

            // Act & Assert
            // Note: This would require a custom validation attribute to check for future dates
            // For now, we'll just verify that the Age property handles it correctly
            Assert.AreEqual(0, _testProfile.Age);
        }

        [TestMethod]
        public void Weight_WithNegativeValue_ShouldBeInvalid()
        {
            // Arrange
            _testProfile.Weight = -1.0f;

            // Act & Assert
            // Note: This would require a Range validation attribute
            // For now, we'll just verify the value is set
            Assert.AreEqual(-1.0f, _testProfile.Weight);
        }

        [TestMethod]
        public void UpdatedAt_ShouldBeSetOnModification()
        {
            // Arrange
            var originalUpdatedAt = _testProfile.UpdatedAt;

            // Act
            _testProfile.Name = "Updated Name";
            _testProfile.UpdatedAt = DateTime.UtcNow;

            // Assert
            Assert.IsTrue(_testProfile.UpdatedAt > originalUpdatedAt);
        }
    }
}